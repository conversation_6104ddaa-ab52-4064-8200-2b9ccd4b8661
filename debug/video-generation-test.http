@baseUrl = http://localhost:3000
@apiKey = sk-xxx

### Test video generation system
GET {{baseUrl}}/api/video/test
Content-Type: application/json
Authorization: Bearer {{apiKey}}

### Generate video - txt2vid
POST {{baseUrl}}/api/video/generate
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "mode": "txt2vid",
  "model": "kling-v1",
  "prompt": "A beautiful sunset over the ocean with gentle waves",
  "negative_prompt": "blurry, low quality",
  "parameters": {
    "resolution": "720p",
    "duration": 5,
    "aspect_ratio": "16:9",
    "cfg_scale": 0.5,
    "mode": "std"
  }
}

### Check video status by job UUID
GET {{baseUrl}}/api/video/status?job_uuid=YOUR_JOB_UUID_HERE
Content-Type: application/json
Authorization: Bearer {{apiKey}}

### Get user's video history
GET {{baseUrl}}/api/video/status?limit=10&offset=0
Content-Type: application/json
Authorization: Bearer {{apiKey}}

### Batch check video status
GET {{baseUrl}}/api/video/status?job_uuids=uuid1,uuid2,uuid3
Content-Type: application/json
Authorization: Bearer {{apiKey}}

### Test with different model
POST {{baseUrl}}/api/video/generate
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "mode": "txt2vid",
  "model": "kling-v1-6",
  "prompt": "A cat playing with a ball of yarn in slow motion",
  "parameters": {
    "resolution": "1080p",
    "duration": 10,
    "aspect_ratio": "16:9",
    "cfg_scale": 0.7,
    "mode": "pro"
  }
}
