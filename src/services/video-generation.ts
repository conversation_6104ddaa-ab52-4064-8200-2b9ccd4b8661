import { getUuid } from "@/lib/hash";
import { newStorage } from "@/lib/storage";
import { newClient } from "@/aisdk/kling/text2video";
import {
  insertGenerationJob,
  updateGenerationJobStatus,
  findGenerationJobByUuid,
  GenerationJobStatus,
  GenerationJobMode,
  GenerationJobParameters,
} from "@/models/generation-job";
import { decreaseCredits, CreditsTransType } from "@/services/credit";
import { calculateVideoCredits } from "@/services/video-credits";

export interface VideoGenerationRequest {
  mode: GenerationJobMode;
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: GenerationJobParameters;
  user_uuid: string;
}

export interface VideoGenerationResponse {
  job_uuid: string;
  status: GenerationJobStatus;
  credits_cost: number;
}

export async function createVideoGenerationJob(
  request: VideoGenerationRequest
): Promise<VideoGenerationResponse> {
  const jobUuid = getUuid();
  
  // 计算所需积分
  const creditsCost = calculateVideoCredits(
    request.model,
    request.parameters.duration || 5,
    request.parameters.resolution || "720p",
    request.parameters.mode || "std",
    1 // 默认生成1个视频
  );

  // 扣除积分
  await decreaseCredits({
    user_uuid: request.user_uuid,
    trans_type: CreditsTransType.VideoGeneration,
    credits: creditsCost,
  });

  // 创建任务记录
  const job = await insertGenerationJob({
    uuid: jobUuid,
    user_uuid: request.user_uuid,
    mode: request.mode,
    model: request.model,
    prompt: request.prompt,
    negative_prompt: request.negative_prompt,
    parameters: request.parameters,
    status: GenerationJobStatus.Pending,
    credits_cost: creditsCost,
    created_at: new Date(),
  });

  if (!job) {
    throw new Error("Failed to create generation job");
  }

  // 异步处理视频生成
  processVideoGeneration(jobUuid).catch(console.error);

  return {
    job_uuid: jobUuid,
    status: GenerationJobStatus.Pending,
    credits_cost: creditsCost,
  };
}

async function processVideoGeneration(jobUuid: string): Promise<void> {
  try {
    const job = await findGenerationJobByUuid(jobUuid);
    if (!job) {
      throw new Error("Job not found");
    }

    // 更新状态为处理中
    await updateGenerationJobStatus(jobUuid, GenerationJobStatus.Processing);

    // 调用 Kling API
    const client = await newClient();
    const klingResponse = await client.createTask({
      model: job.model as any,
      prompt: job.prompt || "",
      negative_prompt: job.negative_prompt || undefined,
      ...(job.parameters as any),
    });

    if (!klingResponse.data?.task_id) {
      throw new Error(`Kling API error: ${klingResponse.message}`);
    }

    const klingTaskId = klingResponse.data.task_id;
    
    // 更新 Kling 任务 ID
    await updateGenerationJob(jobUuid, { kling_task_id: klingTaskId });

    // 轮询任务状态
    await pollKlingTaskStatus(jobUuid, klingTaskId);

  } catch (error) {
    console.error("Video generation failed:", error);
    await updateGenerationJobStatus(jobUuid, GenerationJobStatus.Failed, {
      error_message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

async function pollKlingTaskStatus(jobUuid: string, klingTaskId: string): Promise<void> {
  const client = await newClient();
  const maxAttempts = 60; // 最多轮询60次
  const pollingInterval = 30000; // 30秒间隔
  
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      const response = await client.queryTask({ task_id: klingTaskId });
      
      if (response.data?.status === "succeed" && response.data?.task_result?.videos) {
        // 任务成功，下载并上传视频
        const videoUrl = response.data.task_result.videos[0];
        const r2Url = await downloadAndUploadVideo(jobUuid, videoUrl);
        
        await updateGenerationJobStatus(jobUuid, GenerationJobStatus.Completed, {
          result_url: r2Url,
        });
        return;
      } else if (response.data?.status === "failed") {
        throw new Error(`Kling task failed: ${response.data.task_status_msg || "Unknown error"}`);
      }
      
      // 继续等待
      await new Promise(resolve => setTimeout(resolve, pollingInterval));
      
    } catch (error) {
      console.error(`Polling attempt ${attempt + 1} failed:`, error);
      if (attempt === maxAttempts - 1) {
        throw error;
      }
    }
  }
  
  throw new Error("Task polling timeout");
}

async function downloadAndUploadVideo(jobUuid: string, videoUrl: string): Promise<string> {
  const storage = newStorage();
  const filename = `video_${jobUuid}.mp4`;
  const key = `videos/${filename}`;
  
  const result = await storage.downloadAndUpload({
    url: videoUrl,
    key,
    contentType: "video/mp4",
    disposition: "inline",
  });
  
  return result.url;
}

// 导入缺失的函数
import { updateGenerationJob } from "@/models/generation-job";
