import { getUuid } from "@/lib/hash";
import { newStorage } from "@/lib/storage";
import { generateVideo } from "@/aisdk";
import { kling } from "@/aisdk/kling";
import {
  insertGenerationJob,
  updateGenerationJobStatus,
  findGenerationJobByUuid,
  GenerationJobStatus,
  GenerationJobMode,
  GenerationJobParameters,
} from "@/models/generation-job";
import { decreaseCredits, CreditsTransType } from "@/services/credit";
import { calculateVideoCredits } from "@/services/video-credits";

export interface VideoGenerationRequest {
  mode: GenerationJobMode;
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: GenerationJobParameters;
  user_uuid: string;
}

export interface VideoGenerationResponse {
  job_uuid: string;
  status: GenerationJobStatus;
  credits_cost: number;
}

export async function createVideoGenerationJob(
  request: VideoGenerationRequest
): Promise<VideoGenerationResponse> {
  const jobUuid = getUuid();
  
  // 计算所需积分
  const creditsCost = calculateVideoCredits(
    request.model,
    request.parameters.duration || 5,
    request.parameters.resolution || "720p",
    request.parameters.mode || "std",
    1 // 默认生成1个视频
  );

  // 扣除积分
  await decreaseCredits({
    user_uuid: request.user_uuid,
    trans_type: CreditsTransType.VideoGeneration,
    credits: creditsCost,
  });

  // 创建任务记录
  const job = await insertGenerationJob({
    uuid: jobUuid,
    user_uuid: request.user_uuid,
    mode: request.mode,
    model: request.model,
    prompt: request.prompt,
    negative_prompt: request.negative_prompt,
    parameters: request.parameters,
    status: GenerationJobStatus.Pending,
    credits_cost: creditsCost,
    created_at: new Date(),
  });

  if (!job) {
    throw new Error("Failed to create generation job");
  }

  // 异步处理视频生成
  processVideoGeneration(jobUuid).catch(console.error);

  return {
    job_uuid: jobUuid,
    status: GenerationJobStatus.Pending,
    credits_cost: creditsCost,
  };
}

async function processVideoGeneration(jobUuid: string): Promise<void> {
  try {
    const job = await findGenerationJobByUuid(jobUuid);
    if (!job) {
      throw new Error("Job not found");
    }

    // 更新状态为处理中
    await updateGenerationJobStatus(jobUuid, GenerationJobStatus.Processing);

    // 使用 AI SDK 的 generateVideo 方法
    const videoModel = kling.video(job.model as "kling-v1" | "kling-v1-6");
    const result = await generateVideo({
      model: videoModel,
      prompt: job.prompt || "",
      providerOptions: {
        kling: {
          negative_prompt: job.negative_prompt || undefined,
          ...(job.parameters as any),
        },
      },
    });

    if (result.videos.length === 0) {
      throw new Error("No videos generated");
    }

    // 获取生成的视频
    const video = result.videos[0];

    // 上传视频到 R2
    const r2Url = await uploadVideoToR2(jobUuid, video.uint8Array);

    // 更新任务状态为完成
    await updateGenerationJobStatus(jobUuid, GenerationJobStatus.Completed, {
      result_url: r2Url,
    });

  } catch (error) {
    console.error("Video generation failed:", error);
    await updateGenerationJobStatus(jobUuid, GenerationJobStatus.Failed, {
      error_message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}



async function uploadVideoToR2(jobUuid: string, videoData: Uint8Array): Promise<string> {
  const storage = newStorage();
  const filename = `video_${jobUuid}.mp4`;
  const key = `videos/${filename}`;

  const result = await storage.uploadFile({
    body: videoData,
    key,
    contentType: "video/mp4",
    disposition: "inline",
  });

  return result.url;
}
