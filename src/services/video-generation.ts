import { TaskType, GenerationMode, Provider } from "@/models/generation-job";
import { generationService, GenerationRequest } from "./generation-service";

export interface VideoGenerationRequest {
  task_type: TaskType;
  generation_mode: GenerationMode;
  provider: Provider;
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: Record<string, any>;
  user_uuid: string;
}

export interface VideoGenerationResponse {
  job_uuid: string;
  status: string;
  credits_cost: number;
}

export async function createVideoGenerationJob(
  request: VideoGenerationRequest
): Promise<VideoGenerationResponse> {
  // 使用新的 GenerationService
  const generationRequest: GenerationRequest = {
    task_type: request.task_type,
    generation_mode: request.generation_mode,
    provider: request.provider,
    model: request.model,
    prompt: request.prompt,
    negative_prompt: request.negative_prompt,
    parameters: request.parameters,
    user_uuid: request.user_uuid,
  };

  const result = await generationService.createGenerationJob(generationRequest);

  return {
    job_uuid: result.job_uuid,
    status: result.status,
    credits_cost: result.credits_cost,
  };
}


