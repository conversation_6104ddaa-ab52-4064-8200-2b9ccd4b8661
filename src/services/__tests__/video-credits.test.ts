/**
 * 视频Credits计算功能测试
 */

import {
  calculateVideoCredits,
  calculateVideoCreditsDetailed,
  getModelCreditsInfo,
  getSupportedModels,
  hasEnoughCredits,
  getCreditsDeficit,
  formatCredits,
  getModelRecommendation,
  VIDEO_MODEL_CREDITS,
  DURATION_MULTIPLIERS,
  RESOLUTION_MULTIPLIERS
} from '../video-credits';

describe('Video Credits Calculation', () => {
  describe('calculateVideoCredits', () => {
    test('should calculate basic credits for kling-v1', () => {
      const credits = calculateVideoCredits('kling-v1', 5, '720p', 'std', 1);
      expect(credits).toBe(100); // 100 * 1.0 * 1.0 * 1.0 * 1 = 100
    });

    test('should calculate credits for kling-v1-6', () => {
      const credits = calculateVideoCredits('kling-v1-6', 5, '720p', 'std', 1);
      expect(credits).toBe(150); // 150 * 1.0 * 1.0 * 1.0 * 1 = 150
    });

    test('should apply duration multiplier', () => {
      const credits = calculateVideoCredits('kling-v1', 10, '720p', 'std', 1);
      expect(credits).toBe(200); // 100 * 2.0 * 1.0 * 1.0 * 1 = 200
    });

    test('should apply resolution multiplier', () => {
      const credits = calculateVideoCredits('kling-v1', 5, '1080p', 'std', 1);
      expect(credits).toBe(150); // 100 * 1.0 * 1.5 * 1.0 * 1 = 150
    });

    test('should apply mode multiplier', () => {
      const credits = calculateVideoCredits('kling-v1', 5, '720p', 'pro', 1);
      expect(credits).toBe(180); // 100 * 1.0 * 1.0 * 1.8 * 1 = 180
    });

    test('should apply output count multiplier', () => {
      const credits = calculateVideoCredits('kling-v1', 5, '720p', 'std', 2);
      expect(credits).toBe(200); // 100 * 1.0 * 1.0 * 1.0 * 2 = 200
    });

    test('should handle complex calculation', () => {
      const credits = calculateVideoCredits('kling-v1-6', 10, '1080p', 'pro', 2);
      expect(credits).toBe(810); // 150 * 2.0 * 1.5 * 1.8 * 2 = 810
    });

    test('should handle unknown model', () => {
      const credits = calculateVideoCredits('unknown-model', 5, '720p', 'std', 1);
      expect(credits).toBe(100); // 默认值
    });

    test('should round up fractional credits', () => {
      // 创建一个会产生小数的计算
      const credits = calculateVideoCredits('kling-v1', 5, '720p', 'std', 1);
      expect(Number.isInteger(credits)).toBe(true);
    });
  });

  describe('calculateVideoCreditsDetailed', () => {
    test('should return detailed calculation info', () => {
      const detailed = calculateVideoCreditsDetailed('kling-v1', 5, '720p', 'std', 1);
      
      expect(detailed).toEqual({
        model: 'kling-v1',
        baseCredits: 100,
        multipliers: {
          duration: 1.0,
          resolution: 1.0,
          mode: 1.0,
          outputCount: 1
        },
        calculation: {
          subtotal: 100,
          total: 100
        },
        breakdown: '100 × 1 × 1 × 1 × 1 = 100'
      });
    });
  });

  describe('getModelCreditsInfo', () => {
    test('should return model info for supported model', () => {
      const info = getModelCreditsInfo('kling-v1');
      expect(info).toEqual({
        baseCredits: 100,
        model: 'kling-v1',
        isSupported: true
      });
    });

    test('should return default info for unsupported model', () => {
      const info = getModelCreditsInfo('unknown-model');
      expect(info).toEqual({
        baseCredits: 100,
        model: 'unknown-model',
        isSupported: false
      });
    });
  });

  describe('getSupportedModels', () => {
    test('should return all supported models', () => {
      const models = getSupportedModels();
      expect(models).toEqual(['kling-v1', 'kling-v1-6']);
    });
  });

  describe('hasEnoughCredits', () => {
    test('should return true when user has enough credits', () => {
      expect(hasEnoughCredits(200, 100)).toBe(true);
      expect(hasEnoughCredits(100, 100)).toBe(true);
    });

    test('should return false when user does not have enough credits', () => {
      expect(hasEnoughCredits(50, 100)).toBe(false);
    });
  });

  describe('getCreditsDeficit', () => {
    test('should return 0 when user has enough credits', () => {
      expect(getCreditsDeficit(200, 100)).toBe(0);
      expect(getCreditsDeficit(100, 100)).toBe(0);
    });

    test('should return deficit when user does not have enough credits', () => {
      expect(getCreditsDeficit(50, 100)).toBe(50);
      expect(getCreditsDeficit(0, 100)).toBe(100);
    });
  });

  describe('formatCredits', () => {
    test('should format small numbers as is', () => {
      expect(formatCredits(100)).toBe('100');
      expect(formatCredits(999)).toBe('999');
    });

    test('should format thousands with K suffix', () => {
      expect(formatCredits(1000)).toBe('1.0K');
      expect(formatCredits(1500)).toBe('1.5K');
      expect(formatCredits(999999)).toBe('1000.0K');
    });

    test('should format millions with M suffix', () => {
      expect(formatCredits(1000000)).toBe('1.0M');
      expect(formatCredits(1500000)).toBe('1.5M');
    });
  });

  describe('getModelRecommendation', () => {
    test('should recommend models based on user credits', () => {
      const recommendations = getModelRecommendation(500);
      
      expect(recommendations).toHaveLength(2);
      expect(recommendations[0].id).toBe('kling-v1'); // 更便宜的模型优先
      expect(recommendations[0].recommended).toBe(true);
      expect(recommendations[1].id).toBe('kling-v1-6');
      expect(recommendations[1].recommended).toBe(true);
    });

    test('should not recommend expensive models for low credit users', () => {
      const recommendations = getModelRecommendation(100);
      
      const klingV1 = recommendations.find(r => r.id === 'kling-v1');
      const klingV16 = recommendations.find(r => r.id === 'kling-v1-6');
      
      expect(klingV1?.recommended).toBe(false); // 100 < 200 (100 * 2)
      expect(klingV16?.recommended).toBe(false); // 100 < 300 (150 * 2)
    });
  });

  describe('Constants', () => {
    test('should have correct model credits', () => {
      expect(VIDEO_MODEL_CREDITS['kling-v1']).toBe(100);
      expect(VIDEO_MODEL_CREDITS['kling-v1-6']).toBe(150);
    });

    test('should have correct duration multipliers', () => {
      expect(DURATION_MULTIPLIERS[5]).toBe(1.0);
      expect(DURATION_MULTIPLIERS[10]).toBe(2.0);
    });

    test('should have correct resolution multipliers', () => {
      expect(RESOLUTION_MULTIPLIERS['720p']).toBe(1.0);
      expect(RESOLUTION_MULTIPLIERS['1080p']).toBe(1.5);
    });
  });
});
