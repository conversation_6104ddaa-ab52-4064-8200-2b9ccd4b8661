import { Provider } from "@/models/generation-job";
import { GenerationProvider } from "./generation-provider";
import { KlingProvider } from "./kling-provider";

export class ProviderFactory {
  private static providers: Map<Provider, GenerationProvider> = new Map();

  static {
    // 注册所有可用的 Provider
    this.providers.set(Provider.Kling, new KlingProvider());
    // 未来可以添加更多 Provider
    // this.providers.set(Provider.Runway, new RunwayProvider());
    // this.providers.set(Provider.OpenAI, new OpenAIProvider());
  }

  static getProvider(provider: Provider): GenerationProvider {
    const providerInstance = this.providers.get(provider);
    if (!providerInstance) {
      throw new Error(`Provider ${provider} not found or not supported`);
    }
    return providerInstance;
  }

  static getSupportedProviders(): Provider[] {
    return Array.from(this.providers.keys());
  }

  static isProviderSupported(provider: Provider): boolean {
    return this.providers.has(provider);
  }
}
