import { TaskType, GenerationMode, Provider, GenerationJobStatus } from "@/models/generation-job";

export interface GenerationParams {
  uuid: string;
  user_uuid: string;
  task_type: TaskType;
  generation_mode: GenerationMode;
  provider: Provider;
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: Record<string, any>;
  credits_cost: number;
}

export interface ProviderTask {
  uuid: string;
  status: GenerationJobStatus;
  external_task_id?: string;
  output_files?: any[];
  error_message?: string;
}

export interface TaskStatus {
  status: GenerationJobStatus;
  progress?: number;
  output_files?: any[];
  error_message?: string;
}

export interface GenerationProvider {
  readonly name: string;
  readonly supportedTaskTypes: TaskType[];
  readonly supportedModes: GenerationMode[];
  
  createTask(params: GenerationParams): Promise<ProviderTask>;
  queryTask(taskId: string): Promise<TaskStatus>;
  cancelTask(taskId: string): Promise<void>;
  
  // 可选的进度回调
  onProgress?(callback: (progress: number) => void): void;
}

export abstract class BaseGenerationProvider implements GenerationProvider {
  abstract readonly name: string;
  abstract readonly supportedTaskTypes: TaskType[];
  abstract readonly supportedModes: GenerationMode[];
  
  abstract createTask(params: GenerationParams): Promise<ProviderTask>;
  abstract queryTask(taskId: string): Promise<TaskStatus>;
  
  async cancelTask(taskId: string): Promise<void> {
    // 默认实现：大多数 Provider 可能不支持取消
    throw new Error(`Cancel task not supported by ${this.name}`);
  }
  
  protected validateParams(params: GenerationParams): void {
    if (!this.supportedTaskTypes.includes(params.task_type)) {
      throw new Error(`Task type ${params.task_type} not supported by ${this.name}`);
    }
    
    if (!this.supportedModes.includes(params.generation_mode)) {
      throw new Error(`Generation mode ${params.generation_mode} not supported by ${this.name}`);
    }
  }
}
