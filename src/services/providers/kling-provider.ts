import { generateVideo } from "@/aisdk";
import { kling } from "@/aisdk/kling";
import { newStorage } from "@/lib/storage";
import { TaskType, GenerationMode, Provider, GenerationJobStatus } from "@/models/generation-job";
import { 
  BaseGenerationProvider, 
  GenerationParams, 
  ProviderTask, 
  TaskStatus 
} from "./generation-provider";

export class KlingProvider extends BaseGenerationProvider {
  readonly name = "Kling AI";
  readonly supportedTaskTypes = [TaskType.Video, TaskType.Image];
  readonly supportedModes = [
    GenerationMode.Txt2Vid, 
    GenerationMode.Img2Vid, 
    GenerationMode.Avatar
  ];

  async createTask(params: GenerationParams): Promise<ProviderTask> {
    this.validateParams(params);

    try {
      // 根据任务类型选择对应的模型
      let result;
      if (params.task_type === TaskType.Video) {
        const videoModel = kling.video(params.model as "kling-v1" | "kling-v1-6");
        result = await generateVideo({
          model: videoModel,
          prompt: params.prompt,
          providerOptions: {
            kling: {
              negative_prompt: params.negative_prompt,
              ...params.parameters,
            },
          },
        });
      } else {
        throw new Error(`Task type ${params.task_type} not yet implemented for Kling`);
      }

      if (!result || result.videos.length === 0) {
        throw new Error("No content generated");
      }

      // 上传结果到存储
      const outputFiles = await this.uploadResults(params.uuid, result);

      return {
        uuid: params.uuid,
        status: GenerationJobStatus.Completed,
        output_files: outputFiles,
      };

    } catch (error) {
      console.error("Kling generation failed:", error);
      return {
        uuid: params.uuid,
        status: GenerationJobStatus.Failed,
        error_message: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  async queryTask(taskId: string): Promise<TaskStatus> {
    // Kling 通过 AI SDK 是同步的，所以这里直接返回完成状态
    // 在实际实现中，如果需要异步轮询，可以在这里实现
    return {
      status: GenerationJobStatus.Completed,
      progress: 100,
    };
  }

  private async uploadResults(jobUuid: string, result: any): Promise<any[]> {
    const storage = newStorage();
    const outputFiles = [];

    // 处理视频结果
    if (result.videos && result.videos.length > 0) {
      for (let i = 0; i < result.videos.length; i++) {
        const video = result.videos[i];
        const filename = `video_${jobUuid}_${i}.mp4`;
        const key = `videos/${filename}`;
        
        const uploadResult = await storage.uploadFile({
          body: video.uint8Array,
          key,
          contentType: "video/mp4",
          disposition: "inline",
        });
        
        outputFiles.push({
          type: "video",
          url: uploadResult.url,
          filename,
          contentType: "video/mp4",
        });
      }
    }

    // 处理图片结果（如果有）
    if (result.images && result.images.length > 0) {
      for (let i = 0; i < result.images.length; i++) {
        const image = result.images[i];
        const filename = `image_${jobUuid}_${i}.png`;
        const key = `images/${filename}`;
        
        const uploadResult = await storage.uploadFile({
          body: image.uint8Array,
          key,
          contentType: "image/png",
          disposition: "inline",
        });
        
        outputFiles.push({
          type: "image",
          url: uploadResult.url,
          filename,
          contentType: "image/png",
        });
      }
    }

    return outputFiles;
  }
}
