/**
 * 视频生成Credits计算服务
 */

// 基础模型Credits配置
export const VIDEO_MODEL_CREDITS = {
  'kling-v1': 100,      // Kling V1 基础版本
  'kling-v1-6': 150,    // Kling V1.6 增强版本
} as const;

// 时长倍数配置
export const DURATION_MULTIPLIERS = {
  5: 1.0,   // 5秒基准
  10: 2.0,  // 10秒双倍
} as const;

// 分辨率倍数配置
export const RESOLUTION_MULTIPLIERS = {
  '720p': 1.0,   // 720p基准
  '1080p': 1.5,  // 1080p增加50%
} as const;

// 模式倍数配置
export const MODE_MULTIPLIERS = {
  'std': 1.0,   // 标准模式
  'pro': 1.8,   // 专业模式增加80%
} as const;

// 输出数量倍数
export const OUTPUT_COUNT_MULTIPLIER = 1.0; // 每个额外输出增加100%

/**
 * 计算视频生成所需的Credits
 */
export function calculateVideoCredits(
  model: string,
  duration: number = 5,
  resolution: string = '720p',
  mode: string = 'std',
  outputCount: number = 1
): number {
  // 获取基础Credits
  const baseCredits = VIDEO_MODEL_CREDITS[model as keyof typeof VIDEO_MODEL_CREDITS];
  if (!baseCredits) {
    console.warn(`未知模型: ${model}, 使用默认Credits`);
    return 100;
  }

  // 获取各种倍数
  const durationMult = DURATION_MULTIPLIERS[duration as keyof typeof DURATION_MULTIPLIERS] || 1.0;
  const resolutionMult = RESOLUTION_MULTIPLIERS[resolution as keyof typeof RESOLUTION_MULTIPLIERS] || 1.0;
  const modeMult = MODE_MULTIPLIERS[mode as keyof typeof MODE_MULTIPLIERS] || 1.0;

  // 计算总Credits
  const totalCredits = Math.ceil(
    baseCredits * 
    durationMult * 
    resolutionMult * 
    modeMult * 
    outputCount
  );

  return totalCredits;
}

/**
 * 获取模型的基础Credits信息
 */
export function getModelCreditsInfo(model: string) {
  const baseCredits = VIDEO_MODEL_CREDITS[model as keyof typeof VIDEO_MODEL_CREDITS];
  
  return {
    baseCredits: baseCredits || 100,
    model,
    isSupported: !!baseCredits
  };
}

/**
 * 获取所有支持的模型列表
 */
export function getSupportedModels() {
  return Object.keys(VIDEO_MODEL_CREDITS);
}

/**
 * 计算Credits详细信息（用于调试和显示）
 */
export function calculateVideoCreditsDetailed(
  model: string,
  duration: number = 5,
  resolution: string = '720p',
  mode: string = 'std',
  outputCount: number = 1
) {
  const baseCredits = VIDEO_MODEL_CREDITS[model as keyof typeof VIDEO_MODEL_CREDITS] || 100;
  const durationMult = DURATION_MULTIPLIERS[duration as keyof typeof DURATION_MULTIPLIERS] || 1.0;
  const resolutionMult = RESOLUTION_MULTIPLIERS[resolution as keyof typeof RESOLUTION_MULTIPLIERS] || 1.0;
  const modeMult = MODE_MULTIPLIERS[mode as keyof typeof MODE_MULTIPLIERS] || 1.0;

  const subtotal = baseCredits * durationMult * resolutionMult * modeMult;
  const total = Math.ceil(subtotal * outputCount);

  return {
    model,
    baseCredits,
    multipliers: {
      duration: durationMult,
      resolution: resolutionMult,
      mode: modeMult,
      outputCount
    },
    calculation: {
      subtotal,
      total
    },
    breakdown: `${baseCredits} × ${durationMult} × ${resolutionMult} × ${modeMult} × ${outputCount} = ${total}`
  };
}

/**
 * 验证用户是否有足够的Credits
 */
export function hasEnoughCredits(userCredits: number, requiredCredits: number): boolean {
  return userCredits >= requiredCredits;
}

/**
 * 计算Credits不足的差额
 */
export function getCreditsDeficit(userCredits: number, requiredCredits: number): number {
  return Math.max(0, requiredCredits - userCredits);
}

/**
 * 格式化Credits显示
 */
export function formatCredits(credits: number): string {
  if (credits >= 1000000) {
    return `${(credits / 1000000).toFixed(1)}M`;
  } else if (credits >= 1000) {
    return `${(credits / 1000).toFixed(1)}K`;
  }
  return credits.toString();
}

/**
 * 获取模型推荐配置
 */
export function getModelRecommendation(userCredits: number) {
  const models = [
    {
      id: 'kling-v1',
      name: 'Kling V1',
      baseCredits: VIDEO_MODEL_CREDITS['kling-v1'],
      recommended: userCredits >= VIDEO_MODEL_CREDITS['kling-v1'] * 2
    },
    {
      id: 'kling-v1-6',
      name: 'Kling V1.6',
      baseCredits: VIDEO_MODEL_CREDITS['kling-v1-6'],
      recommended: userCredits >= VIDEO_MODEL_CREDITS['kling-v1-6'] * 2
    }
  ];

  return models.sort((a, b) => {
    // 优先推荐用户Credits足够的模型
    if (a.recommended && !b.recommended) return -1;
    if (!a.recommended && b.recommended) return 1;
    // 其次按Credits从低到高排序
    return a.baseCredits - b.baseCredits;
  });
}
