import { getUuid } from "@/lib/hash";
import {
  insertGenerationJob,
  updateGenerationJobStatus,
  findGenerationJobByUuid,
  GenerationJobStatus,
  TaskType,
  GenerationMode,
  Provider,
} from "@/models/generation-job";
import { decreaseCredits, CreditsTransType } from "@/services/credit";
import { calculateVideoCredits } from "@/services/video-credits";
import { ProviderFactory } from "./providers/provider-factory";
import { GenerationParams } from "./providers/generation-provider";

export interface GenerationRequest {
  task_type: TaskType;
  generation_mode: GenerationMode;
  provider: Provider;
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: Record<string, any>;
  user_uuid: string;
}

export interface GenerationResponse {
  job_uuid: string;
  status: GenerationJobStatus;
  credits_cost: number;
}

export class GenerationService {
  async createGenerationJob(request: GenerationRequest): Promise<GenerationResponse> {
    const jobUuid = getUuid();
    
    // 计算所需积分
    const creditsCost = this.calculateCredits(request);

    // 扣除积分
    await decreaseCredits({
      user_uuid: request.user_uuid,
      trans_type: CreditsTransType.VideoGeneration, // TODO: 根据任务类型选择
      credits: creditsCost,
    });

    // 创建任务记录
    const job = await insertGenerationJob({
      uuid: jobUuid,
      user_uuid: request.user_uuid,
      task_type: request.task_type,
      generation_mode: request.generation_mode,
      provider: request.provider,
      model: request.model,
      prompt: request.prompt,
      negative_prompt: request.negative_prompt,
      parameters: request.parameters,
      status: GenerationJobStatus.Pending,
      credits_cost: creditsCost,
      created_at: new Date(),
    });

    if (!job) {
      throw new Error("Failed to create generation job");
    }

    // 异步处理生成任务
    this.processGeneration(jobUuid).catch(console.error);

    return {
      job_uuid: jobUuid,
      status: GenerationJobStatus.Pending,
      credits_cost: creditsCost,
    };
  }

  private async processGeneration(jobUuid: string): Promise<void> {
    try {
      const job = await findGenerationJobByUuid(jobUuid);
      if (!job) {
        throw new Error("Job not found");
      }

      // 更新状态为处理中
      await updateGenerationJobStatus(jobUuid, GenerationJobStatus.Processing);

      // 获取对应的 Provider
      const provider = ProviderFactory.getProvider(job.provider as Provider);

      // 准备生成参数
      const params: GenerationParams = {
        uuid: job.uuid,
        user_uuid: job.user_uuid,
        task_type: job.task_type as TaskType,
        generation_mode: job.generation_mode as GenerationMode,
        provider: job.provider as Provider,
        model: job.model,
        prompt: job.prompt || "",
        negative_prompt: job.negative_prompt || undefined,
        parameters: job.parameters as Record<string, any> || {},
        credits_cost: job.credits_cost,
      };

      // 调用 Provider 执行生成
      const result = await provider.createTask(params);

      // 更新任务状态
      await updateGenerationJobStatus(jobUuid, result.status, {
        output_files: result.output_files,
        error_message: result.error_message,
      });

    } catch (error) {
      console.error("Generation processing failed:", error);
      await updateGenerationJobStatus(jobUuid, GenerationJobStatus.Failed, {
        error_message: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  private calculateCredits(request: GenerationRequest): number {
    // 根据任务类型计算积分
    switch (request.task_type) {
      case TaskType.Video:
        return calculateVideoCredits(
          request.model,
          request.parameters.duration || 5,
          request.parameters.resolution || "720p",
          request.parameters.mode || "std",
          1
        );
      case TaskType.Image:
        // TODO: 实现图片生成的积分计算
        return 10; // 临时值
      case TaskType.Audio:
        // TODO: 实现音频生成的积分计算
        return 5; // 临时值
      default:
        throw new Error(`Unsupported task type: ${request.task_type}`);
    }
  }
}

// 导出单例实例
export const generationService = new GenerationService();
