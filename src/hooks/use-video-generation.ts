import { useState, useCallback } from "react";
import useS<PERSON> from "swr";
import { toast } from "sonner";

export interface VideoGenerationParams {
  mode: "txt2vid" | "img2vid" | "avatar";
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: {
    resolution?: string;
    duration?: number;
    aspect_ratio?: string;
    cfg_scale?: number;
    mode?: string;
    seed?: number;
    [key: string]: any;
  };
}

export interface VideoGenerationJob {
  uuid: string;
  mode: string;
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: any;
  status: "pending" | "processing" | "completed" | "failed";
  result_url?: string;
  credits_cost: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface VideoGenerationResponse {
  job_uuid: string;
  status: string;
  credits_cost: number;
  message: string;
}

const fetcher = async (url: string) => {
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch");
  }

  const data = await response.json();
  if (data.code !== 0) {
    throw new Error(data.message || "API error");
  }

  return data.data;
};

export function useVideoGeneration() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentJobUuid, setCurrentJobUuid] = useState<string | null>(null);

  const generateVideo = useCallback(async (params: VideoGenerationParams): Promise<string | null> => {
    setIsGenerating(true);
    
    try {
      const response = await fetch("/api/video/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      const data = await response.json();
      
      if (data.code !== 0) {
        throw new Error(data.message || "Generation failed");
      }

      const result: VideoGenerationResponse = data.data;
      setCurrentJobUuid(result.job_uuid);
      
      toast.success(`视频生成任务已创建，消耗 ${result.credits_cost} 积分`);
      
      return result.job_uuid;
    } catch (error) {
      console.error("Video generation error:", error);
      toast.error(error instanceof Error ? error.message : "视频生成失败");
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  return {
    generateVideo,
    isGenerating,
    currentJobUuid,
    setCurrentJobUuid,
  };
}

export function useVideoStatus(jobUuid: string | null, options?: { 
  refreshInterval?: number;
  enabled?: boolean;
}) {
  const { refreshInterval = 5000, enabled = true } = options || {};
  
  const shouldFetch = enabled && jobUuid;
  
  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? `/api/video/status?job_uuid=${jobUuid}` : null,
    fetcher,
    {
      refreshInterval: (data) => {
        // 如果任务已完成或失败，停止轮询
        if (data?.job?.status === "completed" || data?.job?.status === "failed") {
          return 0;
        }
        return refreshInterval;
      },
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
    }
  );

  const job: VideoGenerationJob | null = data?.job || null;

  return {
    job,
    isLoading,
    error,
    mutate,
    isCompleted: job?.status === "completed",
    isFailed: job?.status === "failed",
    isProcessing: job?.status === "processing" || job?.status === "pending",
  };
}

export function useVideoHistory(options?: {
  limit?: number;
  offset?: number;
}) {
  const { limit = 20, offset = 0 } = options || {};
  
  const { data, error, isLoading, mutate } = useSWR(
    `/api/video/status?limit=${limit}&offset=${offset}`,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
    }
  );

  const jobs: VideoGenerationJob[] = data?.jobs || [];
  const pagination = data?.pagination;

  return {
    jobs,
    pagination,
    isLoading,
    error,
    mutate,
  };
}

export function useBatchVideoStatus(jobUuids: string[], options?: {
  refreshInterval?: number;
  enabled?: boolean;
}) {
  const { refreshInterval = 10000, enabled = true } = options || {};
  
  const shouldFetch = enabled && jobUuids.length > 0;
  const uuidsParam = jobUuids.join(",");
  
  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? `/api/video/status?job_uuids=${uuidsParam}` : null,
    fetcher,
    {
      refreshInterval: (data) => {
        // 如果所有任务都已完成或失败，停止轮询
        const jobs: VideoGenerationJob[] = data?.jobs || [];
        const allFinished = jobs.every(job => 
          job.status === "completed" || job.status === "failed"
        );
        return allFinished ? 0 : refreshInterval;
      },
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
    }
  );

  const jobs: VideoGenerationJob[] = data?.jobs || [];

  return {
    jobs,
    isLoading,
    error,
    mutate,
    allCompleted: jobs.every(job => job.status === "completed"),
    anyFailed: jobs.some(job => job.status === "failed"),
    anyProcessing: jobs.some(job => job.status === "processing" || job.status === "pending"),
  };
}
