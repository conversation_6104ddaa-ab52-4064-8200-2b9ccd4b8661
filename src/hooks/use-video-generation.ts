import { useState, useCallback, useEffect, useRef } from "react";
import { toast } from "sonner";

export interface VideoGenerationParams {
  mode: "txt2vid" | "img2vid" | "avatar";
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: {
    resolution?: string;
    duration?: number;
    aspect_ratio?: string;
    cfg_scale?: number;
    mode?: string;
    seed?: number;
    [key: string]: any;
  };
}

export interface VideoGenerationJob {
  uuid: string;
  mode: string;
  model: string;
  prompt: string;
  negative_prompt?: string;
  parameters: any;
  status: "pending" | "processing" | "completed" | "failed";
  result_url?: string;
  credits_cost: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface VideoGenerationResponse {
  job_uuid: string;
  status: string;
  credits_cost: number;
  message: string;
}

const fetchVideoStatus = async (url: string) => {
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch");
  }

  const data = await response.json();
  if (data.code !== 0) {
    throw new Error(data.message || "API error");
  }

  return data.data;
};

export function useVideoGeneration() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentJobUuid, setCurrentJobUuid] = useState<string | null>(null);

  const generateVideo = useCallback(async (params: VideoGenerationParams): Promise<string | null> => {
    setIsGenerating(true);
    
    try {
      const response = await fetch("/api/video/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      const data = await response.json();
      
      if (data.code !== 0) {
        throw new Error(data.message || "Generation failed");
      }

      const result: VideoGenerationResponse = data.data;
      setCurrentJobUuid(result.job_uuid);
      
      toast.success(`视频生成任务已创建，消耗 ${result.credits_cost} 积分`);
      
      return result.job_uuid;
    } catch (error) {
      console.error("Video generation error:", error);
      toast.error(error instanceof Error ? error.message : "视频生成失败");
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  return {
    generateVideo,
    isGenerating,
    currentJobUuid,
    setCurrentJobUuid,
  };
}

export function useVideoStatus(jobUuid: string | null, options?: {
  refreshInterval?: number;
  enabled?: boolean;
}) {
  const { refreshInterval = 5000, enabled = true } = options || {};
  const [job, setJob] = useState<VideoGenerationJob | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchStatus = useCallback(async () => {
    if (!jobUuid || !enabled) return;

    try {
      setIsLoading(true);
      setError(null);
      const data = await fetchVideoStatus(`/api/video/status?job_uuid=${jobUuid}`);
      setJob(data.job);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"));
    } finally {
      setIsLoading(false);
    }
  }, [jobUuid, enabled]);

  const mutate = useCallback(() => {
    fetchStatus();
  }, [fetchStatus]);

  useEffect(() => {
    if (!jobUuid || !enabled) {
      setJob(null);
      return;
    }

    // 立即获取一次状态
    fetchStatus();

    // 设置轮询
    const startPolling = () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      intervalRef.current = setInterval(() => {
        fetchStatus();
      }, refreshInterval);
    };

    startPolling();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [jobUuid, enabled, refreshInterval, fetchStatus]);

  // 如果任务完成或失败，停止轮询
  useEffect(() => {
    if (job?.status === "completed" || job?.status === "failed") {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  }, [job?.status]);

  return {
    job,
    isLoading,
    error,
    mutate,
    isCompleted: job?.status === "completed",
    isFailed: job?.status === "failed",
    isProcessing: job?.status === "processing" || job?.status === "pending",
  };
}

export function useVideoHistory(options?: {
  limit?: number;
  offset?: number;
}) {
  const { limit = 20, offset = 0 } = options || {};
  const [jobs, setJobs] = useState<VideoGenerationJob[]>([]);
  const [pagination, setPagination] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchHistory = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await fetchVideoStatus(`/api/video/status?limit=${limit}&offset=${offset}`);
      setJobs(data.jobs || []);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"));
    } finally {
      setIsLoading(false);
    }
  }, [limit, offset]);

  const mutate = useCallback(() => {
    fetchHistory();
  }, [fetchHistory]);

  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  return {
    jobs,
    pagination,
    isLoading,
    error,
    mutate,
  };
}

export function useBatchVideoStatus(jobUuids: string[], options?: {
  refreshInterval?: number;
  enabled?: boolean;
}) {
  const { refreshInterval = 10000, enabled = true } = options || {};
  const [jobs, setJobs] = useState<VideoGenerationJob[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const shouldFetch = enabled && jobUuids.length > 0;
  const uuidsParam = jobUuids.join(",");

  const fetchBatchStatus = useCallback(async () => {
    if (!shouldFetch) return;

    try {
      setIsLoading(true);
      setError(null);
      const data = await fetchVideoStatus(`/api/video/status?job_uuids=${uuidsParam}`);
      setJobs(data.jobs || []);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"));
    } finally {
      setIsLoading(false);
    }
  }, [shouldFetch, uuidsParam]);

  const mutate = useCallback(() => {
    fetchBatchStatus();
  }, [fetchBatchStatus]);

  useEffect(() => {
    if (!shouldFetch) {
      setJobs([]);
      return;
    }

    // 立即获取一次状态
    fetchBatchStatus();

    // 设置轮询
    const startPolling = () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      intervalRef.current = setInterval(() => {
        fetchBatchStatus();
      }, refreshInterval);
    };

    startPolling();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [shouldFetch, refreshInterval, fetchBatchStatus]);

  // 如果所有任务都完成或失败，停止轮询
  useEffect(() => {
    const allFinished = jobs.length > 0 && jobs.every(job =>
      job.status === "completed" || job.status === "failed"
    );

    if (allFinished && intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, [jobs]);

  return {
    jobs,
    isLoading,
    error,
    mutate,
    allCompleted: jobs.every(job => job.status === "completed"),
    anyFailed: jobs.some(job => job.status === "failed"),
    anyProcessing: jobs.some(job => job.status === "processing" || job.status === "pending"),
  };
}
