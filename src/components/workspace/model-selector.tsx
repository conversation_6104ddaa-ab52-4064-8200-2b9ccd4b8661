"use client";

import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";

interface ModelOption {
  id: string;
  name: string;
  description: string;
  icon?: string;
  badge?: string;
  speed?: string;
}

interface ModelSelectorProps {
  value?: string;
  onValueChange?: (value: string) => void;
  models?: ModelOption[];
  placeholder?: string;
}

// 可用的Kling模型选项
const defaultModels: ModelOption[] = [
  {
    id: "kling-v1",
    name: "Kling V1",
    description: "Fast and cost-effective video generation",
    icon: "RiVideoLine",
    speed: "Fast"
  },
  {
    id: "kling-v1-6",
    name: "Kling V1.6",
    description: "Higher quality with enhanced details",
    icon: "RiFilmLine",
    badge: "Enhanced"
  }
];

export default function ModelSelector({
  value,
  onValueChange,
  models = defaultModels,
  placeholder = "Select a model"
}: ModelSelectorProps) {
  const selectedModel = models.find(model => model.id === value);

  return (
    <div className="space-y-2">
      {/* 复用现有Select组件 */}
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className="w-full bg-background rounded-md h-11">
          <SelectValue placeholder={placeholder}>
            {selectedModel && (
              <div className="flex items-center space-x-2">
                {selectedModel.icon && (
                  <Icon name={selectedModel.icon} className="h-4 w-4" />
                )}
                <span>{selectedModel.name}</span>
                {selectedModel.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {selectedModel.badge}
                  </Badge>
                )}
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent className="bg-background rounded-md">
          {models.map((model) => (
            <SelectItem key={model.id} value={model.id}>
              <div className="flex items-center space-x-2 w-full">
                {model.icon && (
                  <Icon name={model.icon} className="h-4 w-4" />
                )}
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{model.name}</span>
                    {model.badge && (
                      <Badge variant="secondary" className="text-xs">
                        {model.badge}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* 模型描述信息 */}
      {selectedModel && (
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>{selectedModel.description}</span>
          {selectedModel.speed && (
            <Badge variant="outline" className="text-xs">
              {selectedModel.speed}
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
