"use client";

import { useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";

interface AdvancedSettingsProps {
  resolution?: string;
  onResolutionChange?: (value: string) => void;
  duration?: number;
  onDurationChange?: (value: number) => void;
  seed?: string;
  onSeedChange?: (value: string) => void;
  generateAudio?: boolean;
  onGenerateAudioChange?: (value: boolean) => void;
  negativePrompt?: string;
  onNegativePromptChange?: (value: string) => void;
  outputCount?: number;
  onOutputCountChange?: (value: number) => void;
  publicVisibility?: boolean;
  onPublicVisibilityChange?: (value: boolean) => void;
  copyProtection?: boolean;
  onCopyProtectionChange?: (value: boolean) => void;
}

export default function AdvancedSettings({
  resolution = "720p",
  onResolutionChange,
  duration = 8,
  onDurationChange,
  seed = "",
  onSeedChange,
  generateAudio = true,
  onGenerateAudioChange,
  negativePrompt = "",
  onNegativePromptChange,
  outputCount = 1,
  onOutputCountChange,
  publicVisibility = true,
  onPublicVisibilityChange,
  copyProtection = false,
  onCopyProtectionChange
}: AdvancedSettingsProps) {
  const t = useTranslations();
  const [randomSeed, setRandomSeed] = useState(seed || Math.floor(Math.random() * 1000000000).toString());

  // 生成随机种子
  const generateRandomSeed = () => {
    const newSeed = Math.floor(Math.random() * 1000000000).toString();
    setRandomSeed(newSeed);
    onSeedChange?.(newSeed);
  };

  return (
    <div className="w-full">
      {/* 复用现有Accordion组件 */}
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="advanced" className="border-none">
          <AccordionTrigger className="hover:no-underline py-4">
            <div className="flex items-center space-x-2">
              <Icon name="RiSettings3Line" className="h-4 w-4" />
              <span className="font-medium">{t("workspace.advanced.title")}</span>
            </div>
          </AccordionTrigger>
          
          <AccordionContent className="space-y-6 pt-2">
            {/* Generate Audio - 复用Switch */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-sm font-medium">
                  {t("workspace.advanced.generateAudio")}
                </Label>
                <p className="text-xs text-muted-foreground">
                  {t("workspace.advanced.generateAudioDesc")}
                </p>
              </div>
              <Switch
                checked={generateAudio}
                onCheckedChange={onGenerateAudioChange}
              />
            </div>

            {/* Resolution - 复用ToggleGroup */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {t("workspace.advanced.resolution")}
              </Label>
              <ToggleGroup
                type="single"
                value={resolution}
                onValueChange={onResolutionChange}
                className="justify-start"
              >
                <ToggleGroupItem value="720p" className="px-6">
                  720P
                </ToggleGroupItem>
                <ToggleGroupItem value="1080p" className="px-6">
                  1080P
                </ToggleGroupItem>
              </ToggleGroup>
            </div>

            {/* Video Length - 复用Input[range] */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                  {t("workspace.advanced.videoLength")}
                </Label>
                <Badge variant="secondary" className="text-xs">
                  {duration}s
                </Badge>
              </div>
              <Input
                type="range"
                min="1"
                max="10"
                step="1"
                value={duration}
                onChange={(e) => onDurationChange?.(parseInt(e.target.value))}
                className="w-full"
              />
            </div>

            {/* Seed - 复用Input */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {t("workspace.advanced.seed")}
              </Label>
              <div className="flex space-x-2">
                <Input
                  value={seed || randomSeed}
                  onChange={(e) => onSeedChange?.(e.target.value)}
                  placeholder="1261805078"
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={generateRandomSeed}
                  className="shrink-0"
                >
                  <Icon name="RiRefreshLine" className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Negative Prompt - 复用Textarea */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {t("workspace.advanced.negativePrompt")} 
                <span className="text-muted-foreground font-normal">
                  ({t("workspace.advanced.optional")})
                </span>
              </Label>
              <Textarea
                value={negativePrompt}
                onChange={(e) => onNegativePromptChange?.(e.target.value)}
                placeholder={t("workspace.advanced.negativePromptPlaceholder")}
                className="min-h-[80px] resize-none"
              />
            </div>

            {/* Output Video Number - 复用ToggleGroup */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {t("workspace.advanced.outputVideoNumber")}
              </Label>
              <ToggleGroup
                type="single"
                value={outputCount.toString()}
                onValueChange={(value) => onOutputCountChange?.(parseInt(value) || 1)}
                className="justify-start"
              >
                {[1, 2, 3, 4].map((num) => (
                  <ToggleGroupItem key={num} value={num.toString()} className="px-4">
                    {num}
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </div>

            {/* Public Visibility - 复用Switch */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-sm font-medium">
                  {t("workspace.advanced.publicVisibility")}
                </Label>
                <p className="text-xs text-muted-foreground">
                  {t("workspace.advanced.publicVisibilityDesc")}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Icon name="RiEyeLine" className="h-4 w-4 text-muted-foreground" />
                <Switch
                  checked={publicVisibility}
                  onCheckedChange={onPublicVisibilityChange}
                />
              </div>
            </div>

            {/* Copy Protection - 复用Switch */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-sm font-medium">
                  {t("workspace.advanced.copyProtection")}
                </Label>
                <p className="text-xs text-muted-foreground">
                  {t("workspace.advanced.copyProtectionDesc")}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Icon name="RiShieldLine" className="h-4 w-4 text-muted-foreground" />
                <Switch
                  checked={copyProtection}
                  onCheckedChange={onCopyProtectionChange}
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
