"use client";

import { NavItem } from "@/types/blocks/base";
import { Link } from "@/i18n/navigation";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import Icon from "@/components/icon";
import { Badge } from "@/components/ui/badge";

interface WorkspaceSidebarProps {
  navItems: NavItem[];
  isMobile?: boolean;
}

export default function WorkspaceSidebar({ 
  navItems, 
  isMobile = false 
}: WorkspaceSidebarProps) {
  const pathname = usePathname();

  return (
    <aside className={cn(
      "fixed left-0 top-[57px] h-[calc(100vh-57px)] border-r bg-background z-40",
      isMobile ? "w-full" : "w-24"
    )}>
      <div className={cn(
        "flex flex-col space-y-1 p-2",
        isMobile && "p-4"
      )}>
        {/* Navigation Items */}
        <nav className="flex flex-col space-y-1">
          {navItems.map((item, index) => {
            const isActive = pathname.includes(item.url as string);

            return (
              <Link
                key={index}
                href={item.url as any}
                className={cn(
                  buttonVariants({ variant: "ghost" }),
                  isActive
                    ? "bg-primary/10 text-primary hover:bg-primary/20 hover:text-primary"
                    : "hover:bg-muted hover:text-foreground",
                  isMobile 
                    ? "justify-start h-12 px-4" 
                    : "flex-col h-16 w-14 px-2 text-xs"
                )}
              >
                <Icon 
                  name={item.icon || "RiVideoLine"} 
                  className={cn(
                    isMobile ? "h-5 w-5 mr-3" : "h-6 w-6 mb-1"
                  )} 
                />
                <span className={cn(
                  isMobile ? "font-medium" : "text-[10px] leading-tight text-center"
                )}>
                  {item.title}
                </span>
                
                {/* Coming Soon Badge for non-txt2vid items */}
                {/* {!item.url?.includes("txt2vid") && (
                  <Badge 
                    variant="secondary" 
                    className={cn(
                      "text-[8px] px-1 py-0",
                      isMobile ? "ml-auto" : "absolute -top-1 -right-1"
                    )}
                  >
                    Soon
                  </Badge>
                )} */}
              </Link>
            );
          })}
        </nav>

        {/* Divider */}
        <div className="border-t my-4" />

        {/* Additional Actions */}
        <div className="flex flex-col space-y-1">
          <Link
            href="/history"
            className={cn(
              buttonVariants({ variant: "ghost" }),
              "hover:bg-muted hover:text-foreground",
              isMobile 
                ? "justify-start h-12 px-4" 
                : "flex-col h-16 w-14 px-2 text-xs"
            )}
          >
            <Icon 
              name="RiHistoryLine" 
              className={cn(
                isMobile ? "h-5 w-5 mr-3" : "h-6 w-6 mb-1"
              )} 
            />
            <span className={cn(
              isMobile ? "font-medium" : "text-[10px] leading-tight text-center"
            )}>
              History
            </span>
          </Link>

          <Link
            href="/settings"
            className={cn(
              buttonVariants({ variant: "ghost" }),
              "hover:bg-muted hover:text-foreground",
              isMobile 
                ? "justify-start h-12 px-4" 
                : "flex-col h-16 w-14 px-2 text-xs"
            )}
          >
            <Icon 
              name="RiSettingsLine" 
              className={cn(
                isMobile ? "h-5 w-5 mr-3" : "h-6 w-6 mb-1"
              )} 
            />
            <span className={cn(
              isMobile ? "font-medium" : "text-[10px] leading-tight text-center"
            )}>
              Settings
            </span>
          </Link>
        </div>
      </div>
    </aside>
  );
}
