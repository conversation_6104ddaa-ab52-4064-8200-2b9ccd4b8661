"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { formatCredits, calculateVideoCreditsDetailed } from "@/services/video-credits";
import { useTranslations } from "next-intl";
import Icon from "@/components/icon";

interface CreditsDisplayProps {
  userCredits: number;
  requiredCredits: number;
  model: string;
  duration: number;
  resolution: string;
  outputCount: number;
  showDetailed?: boolean;
}

export default function CreditsDisplay({
  userCredits,
  requiredCredits,
  model,
  duration,
  resolution,
  outputCount,
  showDetailed = false
}: CreditsDisplayProps) {
  const t = useTranslations();
  
  const hasEnough = userCredits >= requiredCredits;
  const deficit = Math.max(0, requiredCredits - userCredits);
  
  const detailedInfo = showDetailed ? calculateVideoCreditsDetailed(
    model,
    duration,
    resolution,
    'std',
    outputCount
  ) : null;

  return (
    <Card className="border-dashed">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Credits概览 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name="RiCoinLine" className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Credits Required</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge 
                variant={hasEnough ? "default" : "destructive"}
                className="font-mono"
              >
                {formatCredits(requiredCredits)}
              </Badge>
            </div>
          </div>

          {/* 用户余额 */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Your Balance</span>
            <span className={`font-mono ${hasEnough ? 'text-green-600' : 'text-red-600'}`}>
              {formatCredits(userCredits)}
            </span>
          </div>

          {/* 不足提示 */}
          {!hasEnough && (
            <div className="flex items-center space-x-2 p-2 bg-red-50 dark:bg-red-950/20 rounded-md">
              <Icon name="RiErrorWarningLine" className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-600 dark:text-red-400">
                Need {formatCredits(deficit)} more credits
              </span>
            </div>
          )}

          {/* 详细计算信息 */}
          {showDetailed && detailedInfo && (
            <>
              <Separator />
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-muted-foreground">
                  Calculation Breakdown
                </h4>
                <div className="space-y-1 text-xs text-muted-foreground font-mono">
                  <div className="flex justify-between">
                    <span>Base ({model}):</span>
                    <span>{detailedInfo.baseCredits}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Duration ({duration}s):</span>
                    <span>×{detailedInfo.multipliers.duration}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Resolution ({resolution}):</span>
                    <span>×{detailedInfo.multipliers.resolution}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Output Count ({outputCount}):</span>
                    <span>×{detailedInfo.multipliers.outputCount}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-medium text-foreground">
                    <span>Total:</span>
                    <span>{detailedInfo.calculation.total}</span>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* 模型推荐 */}
          {!hasEnough && (
            <div className="p-2 bg-blue-50 dark:bg-blue-950/20 rounded-md">
              <div className="flex items-center space-x-2 mb-1">
                <Icon name="RiLightbulbLine" className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                  Suggestion
                </span>
              </div>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                Try using Kling V1 or reduce duration to save credits
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// 简化版Credits显示组件
export function CreditsIndicator({ 
  userCredits, 
  requiredCredits 
}: { 
  userCredits: number; 
  requiredCredits: number; 
}) {
  const hasEnough = userCredits >= requiredCredits;
  
  return (
    <div className="flex items-center space-x-2">
      <Icon 
        name="RiCoinLine" 
        className={`h-4 w-4 ${hasEnough ? 'text-green-500' : 'text-red-500'}`} 
      />
      <span className="text-sm">
        <span className={hasEnough ? 'text-green-600' : 'text-red-600'}>
          {formatCredits(requiredCredits)}
        </span>
        <span className="text-muted-foreground"> / {formatCredits(userCredits)}</span>
      </span>
    </div>
  );
}
