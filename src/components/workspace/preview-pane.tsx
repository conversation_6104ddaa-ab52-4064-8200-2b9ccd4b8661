"use client";

import { <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import Icon from "@/components/icon";

import { useVideoStatus } from "@/hooks/use-video-generation";
import { useState, useEffect } from "react";

interface PreviewPaneProps {
  mode: "txt2vid" | "img2vid" | "avatar";
  currentJobUuid?: string | null;
}

export default function PreviewPane({ mode, currentJobUuid }: PreviewPaneProps) {
  const t = useTranslations();
  const { job, isLoading, isCompleted, isFailed, isProcessing } = useVideoStatus(currentJobUuid || null);
  const [progress, setProgress] = useState(0);

  // 模拟进度更新
  useEffect(() => {
    if (isProcessing && !isCompleted && !isFailed) {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 2000);
      return () => clearInterval(interval);
    } else if (isCompleted) {
      setProgress(100);
    } else if (isFailed) {
      setProgress(0);
    }
  }, [isProcessing, isCompleted, isFailed]);

  return (
    <div className="flex flex-col h-full">
      {/* Fixed Header */}
      <CardHeader className="flex-shrink-0 pb-3 px-4 pt-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            Sample Video
          </h3>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              <Icon name="RiEyeLine" className="w-3 h-3 mr-1" />
              Preview
            </Badge>
            <Button variant="ghost" size="sm">
              <Icon name="RiFullscreenLine" className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* Flexible Content */}
      <CardContent className="flex-1 min-h-0 p-4">
        <div className="flex flex-col h-full">
          {/* Video Preview Area */}
          <div className="flex-1 flex items-center justify-center">
            {isCompleted && job?.output_files && job.output_files.length > 0 ? (
              <div className="w-full max-w-2xl">
                <video
                  controls
                  className="w-full aspect-video rounded-lg"
                  src={job.output_files[0].url}
                >
                  Your browser does not support the video tag.
                </video>
              </div>
            ) : isProcessing ? (
              <div className="w-full max-w-2xl aspect-video bg-muted/30 rounded-lg border-2 border-dashed border-muted-foreground/25 flex flex-col items-center justify-center">
                <Icon name="RiLoader4Line" className="w-16 h-16 text-muted-foreground mb-4 animate-spin" />
                <h4 className="text-lg font-medium text-muted-foreground mb-2">
                  生成中...
                </h4>
                <p className="text-sm text-muted-foreground text-center max-w-sm">
                  请耐心等待，视频生成通常需要几分钟时间
                </p>
                <div className="w-full max-w-xs mt-4">
                  <div className="bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-2 text-center">
                    {Math.round(progress)}% 完成
                  </p>
                </div>
              </div>
            ) : isFailed ? (
              <div className="w-full max-w-2xl aspect-video bg-red-50 rounded-lg border-2 border-dashed border-red-200 flex flex-col items-center justify-center">
                <Icon name="RiErrorWarningLine" className="w-16 h-16 text-red-500 mb-4" />
                <h4 className="text-lg font-medium text-red-700 mb-2">
                  生成失败
                </h4>
                <p className="text-sm text-red-600 text-center max-w-sm">
                  {job?.error_message || "生成过程中出现错误，请重试"}
                </p>
              </div>
            ) : (
              <div className="w-full max-w-2xl aspect-video bg-muted/30 rounded-lg border-2 border-dashed border-muted-foreground/25 flex flex-col items-center justify-center">
                <Icon name="RiVideoLine" className="w-16 h-16 text-muted-foreground mb-4" />
                <h4 className="text-lg font-medium text-muted-foreground mb-2">
                  {t("workspace.previewPane.noVideo")}
                </h4>
                <p className="text-sm text-muted-foreground text-center max-w-sm">
                  {t("workspace.previewPane.noVideoDescription")}
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          {isCompleted && job?.output_files && job.output_files.length > 0 && (
            <div className="mt-6 pt-6 border-t">
              <div className="flex items-center justify-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = job.output_files[0].url;
                    link.download = `video_${job.uuid}.mp4`;
                    link.click();
                  }}
                >
                  <Icon name="RiDownloadLine" className="w-4 h-4 mr-2" />
                  {t("workspace.previewPane.download")}
                </Button>
                <Button variant="outline" size="sm" disabled>
                  <Icon name="RiHeartLine" className="w-4 h-4 mr-2" />
                  {t("workspace.previewPane.favorite")}
                </Button>
                <Button variant="outline" size="sm" disabled>
                  <Icon name="RiShareLine" className="w-4 h-4 mr-2" />
                  {t("workspace.previewPane.share")}
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </div>
  );
}
