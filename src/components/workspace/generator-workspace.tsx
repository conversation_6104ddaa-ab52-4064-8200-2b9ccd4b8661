"use client";

import { Card } from "@/components/ui/card";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";
import { useState } from "react";
import ParamPanel from "./param-panel";
import PreviewPane from "./preview-pane";

interface GeneratorWorkspaceProps {
  mode: "txt2vid" | "img2vid" | "avatar";
  title: string;
}

export default function GeneratorWorkspace({
  mode,
  title,
}: GeneratorWorkspaceProps) {
  const isMobile = useMediaQuery("(max-width: 1024px)");
  const [currentJobUuid, setCurrentJobUuid] = useState<string | null>(null);

  return (
    <div className="h-full p-3">
      <div className={cn(
        "flex gap-3 h-full",
        isMobile && "flex-col space-y-3"
      )}>
        {/* Left Panel: Parameters - Fixed Width */}
        <div className={cn(
          "w-96 flex-shrink-0",
          isMobile && "w-full"
        )}>
          <Card className="h-full">
            <ParamPanel
              mode={mode}
              title={title}
              onJobCreated={setCurrentJobUuid}
            />
          </Card>
        </div>

        {/* Right Panel: Preview - Flexible Width, Same Height */}
        <div className="flex-1 min-w-0">
          <Card className="h-full">
            <PreviewPane
              mode={mode}
              currentJobUuid={currentJobUuid}
            />
          </Card>
        </div>
      </div>
    </div>
  );
}
