import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { 
  findGenerationJobByUuid,
  findGenerationJobsByUuids,
  findGenerationJobsByUser 
} from "@/models/generation-job";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const jobUuid = searchParams.get("job_uuid");
    const jobUuids = searchParams.get("job_uuids");
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");

    // 获取用户信息
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    // 单个任务查询
    if (jobUuid) {
      const job = await findGenerationJobByUuid(jobUuid);
      if (!job) {
        return respErr("Job not found");
      }

      // 验证任务所有权
      if (job.user_uuid !== userUuid) {
        return respErr("Access denied");
      }

      return respData({
        job: {
          uuid: job.uuid,
          task_type: job.task_type,
          generation_mode: job.generation_mode,
          provider: job.provider,
          model: job.model,
          prompt: job.prompt,
          negative_prompt: job.negative_prompt,
          parameters: job.parameters,
          status: job.status,
          output_files: job.output_files,
          credits_cost: job.credits_cost,
          created_at: job.created_at,
          completed_at: job.completed_at,
          error_message: job.error_message,
        }
      });
    }

    // 批量任务查询
    if (jobUuids) {
      const uuidArray = jobUuids.split(",").filter(Boolean);
      if (uuidArray.length === 0) {
        return respErr("Invalid job_uuids parameter");
      }

      const jobs = await findGenerationJobsByUuids(uuidArray);
      
      // 过滤只返回当前用户的任务
      const userJobs = jobs.filter(job => job.user_uuid === userUuid);

      return respData({
        jobs: userJobs.map(job => ({
          uuid: job.uuid,
          task_type: job.task_type,
          generation_mode: job.generation_mode,
          provider: job.provider,
          model: job.model,
          prompt: job.prompt,
          negative_prompt: job.negative_prompt,
          parameters: job.parameters,
          status: job.status,
          output_files: job.output_files,
          credits_cost: job.credits_cost,
          created_at: job.created_at,
          completed_at: job.completed_at,
          error_message: job.error_message,
        }))
      });
    }

    // 用户任务列表查询
    const jobs = await findGenerationJobsByUser(userUuid, limit, offset);

    return respData({
      jobs: jobs.map(job => ({
        uuid: job.uuid,
        task_type: job.task_type,
        generation_mode: job.generation_mode,
        provider: job.provider,
        model: job.model,
        prompt: job.prompt,
        negative_prompt: job.negative_prompt,
        parameters: job.parameters,
        status: job.status,
        output_files: job.output_files,
        credits_cost: job.credits_cost,
        created_at: job.created_at,
        completed_at: job.completed_at,
        error_message: job.error_message,
      })),
      pagination: {
        limit,
        offset,
        has_more: jobs.length === limit,
      }
    });

  } catch (error) {
    console.error("Video status API error:", error);
    return respErr("Failed to fetch video status");
  }
}
