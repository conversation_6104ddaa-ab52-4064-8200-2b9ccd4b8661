import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { calculateVideoCredits } from "@/services/video-credits";

export async function GET(req: Request) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    // 测试积分计算
    const testCredits = calculateVideoCredits("kling-v1", 5, "720p", "std", 1);
    
    return respData({
      message: "Video generation system is ready",
      user_uuid: userUuid,
      test_credits: testCredits,
      supported_models: ["kling-v1", "kling-v1-6"],
      supported_modes: ["txt2vid", "img2vid", "avatar"],
      api_endpoints: {
        generate: "/api/video/generate",
        status: "/api/video/status",
      }
    });

  } catch (error) {
    console.error("Video test API error:", error);
    return respErr("Test failed");
  }
}
