import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";
import { calculateVideoCredits } from "@/services/video-credits";
import { 
  createVideoGenerationJob,
  VideoGenerationRequest 
} from "@/services/video-generation";
import { GenerationJobMode } from "@/models/generation-job";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { 
      mode, 
      model, 
      prompt, 
      negative_prompt,
      parameters = {} 
    } = body;

    // 验证必需参数
    if (!mode || !model || !prompt) {
      return respErr("Missing required parameters: mode, model, prompt");
    }

    // 验证模式
    if (!Object.values(GenerationJobMode).includes(mode)) {
      return respErr("Invalid mode. Must be one of: txt2vid, img2vid, avatar");
    }

    // 获取用户信息
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    // 计算所需积分
    const creditsCost = calculateVideoCredits(
      model,
      parameters.duration || 5,
      parameters.resolution || "720p", 
      parameters.mode || "std",
      1
    );

    // 检查用户积分余额
    const userCredits = await getUserCredits(userUuid);
    if (userCredits < creditsCost) {
      return respErr(`Insufficient credits. Required: ${creditsCost}, Available: ${userCredits}`);
    }

    // 创建视频生成任务
    const request: VideoGenerationRequest = {
      mode,
      model,
      prompt,
      negative_prompt,
      parameters,
      user_uuid: userUuid,
    };

    const result = await createVideoGenerationJob(request);

    return respData({
      job_uuid: result.job_uuid,
      status: result.status,
      credits_cost: result.credits_cost,
      message: "Video generation job created successfully",
    });

  } catch (error) {
    console.error("Video generation API error:", error);
    
    if (error instanceof Error) {
      // 处理特定错误类型
      if (error.message.includes("Insufficient credits")) {
        return respErr(error.message);
      }
      if (error.message.includes("Failed to create generation job")) {
        return respErr("Failed to create generation job. Please try again.");
      }
    }

    return respErr("Video generation failed. Please try again later.");
  }
}
