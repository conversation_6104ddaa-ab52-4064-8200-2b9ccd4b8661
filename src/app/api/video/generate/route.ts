import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";
import { TaskType, GenerationMode, Provider } from "@/models/generation-job";
import { generationService, GenerationRequest } from "@/services/generation-service";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { 
      mode, 
      model, 
      prompt, 
      negative_prompt,
      parameters = {} 
    } = body;

    // 验证必需参数
    if (!mode || !model || !prompt) {
      return respErr("Missing required parameters: mode, model, prompt");
    }

    // 验证模式
    if (!Object.values(GenerationMode).includes(mode)) {
      return respErr("Invalid mode. Must be one of: txt2vid, img2vid, avatar");
    }

    // 验证模型
    const supportedModels = ["kling-v1", "kling-v1-6"];
    if (!supportedModels.includes(model)) {
      return respErr(`Invalid model. Must be one of: ${supportedModels.join(", ")}`);
    }

    // 获取用户信息
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    // 检查用户积分余额 - 这里先做一个简单的检查，具体扣费在 GenerationService 中处理
    const userCredits = await getUserCredits(userUuid);
    if (userCredits.left_credits < 10) { // 临时的最小积分检查
      return respErr(`Insufficient credits. Available: ${userCredits.left_credits}`);
    }

    // 创建视频生成任务
    const request: GenerationRequest = {
      task_type: TaskType.Video,
      generation_mode: mode as GenerationMode,
      provider: Provider.Kling, // 默认使用 Kling，后续可以从参数中获取
      model,
      prompt,
      negative_prompt,
      parameters,
      user_uuid: userUuid,
    };

    const result = await generationService.createGenerationJob(request);

    return respData({
      job_uuid: result.job_uuid,
      status: result.status,
      credits_cost: result.credits_cost,
      message: "Video generation job created successfully",
    });

  } catch (error) {
    console.error("Video generation API error:", error);
    
    if (error instanceof Error) {
      // 处理特定错误类型
      if (error.message.includes("Insufficient credits")) {
        return respErr(error.message);
      }
      if (error.message.includes("Failed to create generation job")) {
        return respErr("Failed to create generation job. Please try again.");
      }
    }

    return respErr("Video generation failed. Please try again later.");
  }
}
