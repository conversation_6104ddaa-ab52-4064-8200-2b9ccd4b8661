import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";
import { TaskType, GenerationMode, Provider } from "@/models/generation-job";
import { generationService, GenerationRequest } from "@/services/generation-service";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const {
      task_type,
      generation_mode,
      provider,
      model,
      prompt,
      negative_prompt,
      parameters = {}
    } = body;

    // 验证必需参数
    if (!task_type || !generation_mode || !provider || !model || !prompt) {
      return respErr("Missing required parameters: task_type, generation_mode, provider, model, prompt");
    }

    // 验证任务类型
    if (!Object.values(TaskType).includes(task_type)) {
      return respErr("Invalid task_type. Must be one of: video, image, audio");
    }

    // 验证生成模式
    if (!Object.values(GenerationMode).includes(generation_mode)) {
      return respErr("Invalid generation_mode. Must be one of: txt2vid, img2vid, avatar, txt2img, img2img");
    }

    // 验证提供商
    if (!Object.values(Provider).includes(provider)) {
      return respErr("Invalid provider. Must be one of: kling, runway, pika, openai, midjourney");
    }

    // 验证模型
    const supportedModels = ["kling-v1", "kling-v1-6"];
    if (!supportedModels.includes(model)) {
      return respErr(`Invalid model. Must be one of: ${supportedModels.join(", ")}`);
    }

    // 获取用户信息
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    // 检查用户积分余额 - 这里先做一个简单的检查，具体扣费在 GenerationService 中处理
    const userCredits = await getUserCredits(userUuid);
    if (userCredits.left_credits < 10) { // 临时的最小积分检查
      return respErr(`Insufficient credits. Available: ${userCredits.left_credits}`);
    }

    // 创建视频生成任务
    const request: GenerationRequest = {
      task_type: task_type as TaskType,
      generation_mode: generation_mode as GenerationMode,
      provider: provider as Provider,
      model,
      prompt,
      negative_prompt,
      parameters,
      user_uuid: userUuid,
    };

    const result = await generationService.createGenerationJob(request);

    return respData({
      job_uuid: result.job_uuid,
      status: result.status,
      credits_cost: result.credits_cost,
      message: "Video generation job created successfully",
    });

  } catch (error) {
    console.error("Video generation API error:", error);
    
    if (error instanceof Error) {
      // 处理特定错误类型
      if (error.message.includes("Insufficient credits")) {
        return respErr(error.message);
      }
      if (error.message.includes("Failed to create generation job")) {
        return respErr("Failed to create generation job. Please try again.");
      }
    }

    return respErr("Video generation failed. Please try again later.");
  }
}
