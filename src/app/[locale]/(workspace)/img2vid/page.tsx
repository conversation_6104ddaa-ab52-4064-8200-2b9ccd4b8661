import { getTranslations } from "next-intl/server";
import GeneratorWorkspace from "@/components/workspace/generator-workspace";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/img2vid`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/img2vid`;
  }

  return {
    title: t("workspace.img2vid.title"),
    description: t("workspace.img2vid.description"),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function Img2VidPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();

  return (
    <GeneratorWorkspace 
      mode="img2vid"
      title={t("workspace.img2vid.title")}
      description={t("workspace.img2vid.description")}
    />
  );
}
