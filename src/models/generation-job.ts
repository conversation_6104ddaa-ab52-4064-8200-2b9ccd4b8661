import { generationJobs } from "@/db/schema";
import { db } from "@/db";
import { and, desc, eq, inArray } from "drizzle-orm";

export enum GenerationJobStatus {
  Pending = "pending",
  Processing = "processing", 
  Completed = "completed",
  Failed = "failed",
}

export enum GenerationJobMode {
  Txt2Vid = "txt2vid",
  Img2Vid = "img2vid", 
  Avatar = "avatar",
}

export interface GenerationJobParameters {
  resolution?: string;
  duration?: number;
  aspect_ratio?: string;
  cfg_scale?: number;
  mode?: string;
  seed?: number;
  negative_prompt?: string;
  [key: string]: any;
}

export async function insertGenerationJob(
  data: typeof generationJobs.$inferInsert
): Promise<typeof generationJobs.$inferSelect | undefined> {
  const [job] = await db().insert(generationJobs).values(data).returning();
  return job;
}

export async function updateGenerationJob(
  uuid: string,
  data: Partial<typeof generationJobs.$inferInsert>
): Promise<typeof generationJobs.$inferSelect | undefined> {
  const [job] = await db()
    .update(generationJobs)
    .set(data)
    .where(eq(generationJobs.uuid, uuid))
    .returning();
  return job;
}

export async function findGenerationJobByUuid(
  uuid: string
): Promise<typeof generationJobs.$inferSelect | undefined> {
  const [job] = await db()
    .select()
    .from(generationJobs)
    .where(eq(generationJobs.uuid, uuid))
    .limit(1);
  return job;
}

export async function findGenerationJobsByUuids(
  uuids: string[]
): Promise<typeof generationJobs.$inferSelect[]> {
  if (uuids.length === 0) return [];
  
  return await db()
    .select()
    .from(generationJobs)
    .where(inArray(generationJobs.uuid, uuids));
}

export async function findGenerationJobsByUser(
  userUuid: string,
  limit: number = 20,
  offset: number = 0
): Promise<typeof generationJobs.$inferSelect[]> {
  return await db()
    .select()
    .from(generationJobs)
    .where(eq(generationJobs.user_uuid, userUuid))
    .orderBy(desc(generationJobs.created_at))
    .limit(limit)
    .offset(offset);
}

export async function findPendingGenerationJobs(): Promise<typeof generationJobs.$inferSelect[]> {
  return await db()
    .select()
    .from(generationJobs)
    .where(eq(generationJobs.status, GenerationJobStatus.Pending))
    .orderBy(generationJobs.created_at);
}

export async function findProcessingGenerationJobs(): Promise<typeof generationJobs.$inferSelect[]> {
  return await db()
    .select()
    .from(generationJobs)
    .where(eq(generationJobs.status, GenerationJobStatus.Processing))
    .orderBy(generationJobs.created_at);
}

export async function updateGenerationJobStatus(
  uuid: string,
  status: GenerationJobStatus,
  additionalData?: {
    result_url?: string;
    error_message?: string;
    completed_at?: Date;
  }
): Promise<typeof generationJobs.$inferSelect | undefined> {
  const updateData: Partial<typeof generationJobs.$inferInsert> = {
    status,
    ...additionalData,
  };

  if (status === GenerationJobStatus.Completed && !additionalData?.completed_at) {
    updateData.completed_at = new Date();
  }

  return updateGenerationJob(uuid, updateData);
}
