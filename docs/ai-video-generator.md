下面把 Pollo.ai Video Generator 这一工作区型页面拆成「框架-组件-状态-样式-交互-可扩展」六大块，给你一份 Next 19 + shadcn/ui + Tailwind 可直接落地的设计／开发蓝图。照着搭骨架，后续 Img2Vid、Avatar… 都能在同一套壳里横向复用。

⸻
0 页面图片


1 页面框架（Layout Blueprint）

┌─<AppShell>──────────────────────────────────────────────┐
│  TopBar (Logo, BreadCrumb, AccountMenu, CTA)           │
├─<Main>──────────────┬───────────────────────────────────┤
│  SideNav            │  <WorkspaceArea>                 │
│  (icon+label)       │  ├─ParamPanel (left)             │
│  Img2Vid            │  │                               │
│  Txt2Vid (active)   │  │                               │
│  Avatar             │  │                               │
│  Motion …           │  └─PreviewPane (right)           │
└─────────────────────┴───────────────────────────────────┘

	•	SideNav 固定 72 px；图标 + label；当前路由高亮。
	•	ParamPanel min-w 24rem，scroll-y；max-w-sm 保证极窄屏仍能露出。
	•	PreviewPane flex-1；横向撑满；视频中央居中；下方放进度 / 多结果网格。
	•	移动端 ≤ md：SideNav 折叠为汉堡，ParamPanel 用 <Sheet>（shadcn）抽屉；Preview 全屏。

⸻

2 组件树（可复用切片）

组件	作用	复用点
AppShell	顶栏 + 左侧导航壳	全站
SideNavItem[]	√ 图标、label、href、badge	JSON 数据驱动
GeneratorForm	文案中 “Txt2Vid” 整个表单	换模式只改字段映射
ModelSelector	Select + Logo + 速度说明	各模式通用
PromptInput	textarea + token 计数 + 翻译开关	通用
AdvancedAccordion	Collapsible + 滑块 / Switch 列	通用
GenerateButton	Credits 计算 + Disable 状态	通用
PreviewPlayer	 + poster + controls	通用
ProgressOverlay	Lottie / Skeleton + 百分比	通用
OutputGrid	N→1/4 卡片；支持收藏 / 下载 / R2 link	通用

设计原则：把逻辑解耦到 hooks/useGenerate.ts（发任务、轮询）、stores/generator.ts（Zustand 全局可被多模式共用）——UI 组件只负责展示 & 触发事件。

⸻

3 状态与数据流

[UI Form] ──→ mutate() ──POST /api/video/generate──→ DB
             ←─ SWR mutate / 轮询 / SSE ←──────────JobStatus

	1.	useGenerate()

const { run, statusMap } = useGenerate(mode) // mode = txt2vid

	•	run(formValues): 调 Pollo /generation/<model> 创建任务 → 写 generation_job → 返回 id。
	•	statusMap：SWR 拉 /api/video/status?taskIds=… 或 WebSocket subscribe → 更新 UI。

	2.	实时进度
	•	前端先显示 ProgressOverlay；status 到 succeed 后切 PreviewPlayer。
	•	failed 时 toast + 红色条。
	3.	信用点计算
	•	credits = baseCredits[model] * durationMult * ratioMult（常量表）。
	•	GenerateButton 上同时渲染 “Credits required: 170”。

⸻

4 样式与主题
	•	基色 = 先前定义的 pollo-dark CSS 变量：
	•	背景 hsl(var(--background)) (#0B0D14)
	•	主色 hsl(var(--primary)) (#E5007D)
	•	辅色 hsl(var(--secondary)) (#06B6D4)
	•	尺寸 （Tailwind Token）：
	•	SideNav w-18、TopBar h-14
	•	ParamPanel max-w-sm
	•	按钮 h-11 px-6 rounded-full
	•	阴影与层次 ：卡片 bg-card/75 backdrop-blur border border-border, hover:shadow-lg.
	•	动画 ： shadcn Accordion 默认；生成按钮用 framer-motion scale-tap。

⸻

5 交互微细节（与 Pollo 对齐）

元素	行为	Tailwind/shadcn 实现
Model Select	点击展开 <Command + Popover>；下方说明行“30 % Faster…”	Popover > Command > CommandItem
PromptInput	1500 字限制；底部 token 计数；右上角 Copy/Translate Switch	Textarea + FormField
Generate with AI	GPT-Assist 插图标，点击补全 prompt	次要按钮，用 service 调 GPT
Advanced Accordion	默认折叠，展开平滑高度	shadcn Accordion
Resolution	720p / 1080p pill 切换	ToggleGroup
Video Length	Slider step 1s，label 8s	shadcn Slider
Seed	Input 占满	若为空随机
Credits Badge	动态颜色 text-pink-400 if balance < cost	


⸻

6 文件结构建议（/app）

/app
└── (workspace)
    ├── layout.tsx          // AppShell
    ├── page.tsx            // /txt2vid default
    ├── txt2vid/page.tsx    // GeneratorPage(mode="txt2vid")
    ├── img2vid/page.tsx
    ├── avatar/page.tsx     ...
    ├── components/
    │   ├── GeneratorForm.tsx
    │   ├── ModelSelector.tsx
    │   ├── PromptInput.tsx
    │   ├── AdvancedAccordion.tsx
    │   ├── PreviewPlayer.tsx
    │   └── ...
    ├── hooks/useGenerate.ts
    └── stores/generator.ts


⸻

7 开发里程碑（功能优先）
	1.	搭壳：AppShell + SideNav + empty Workspace
	2.	GeneratorForm MVP：Model, Prompt, Generate button → 调 /api/video/generate
	3.	Preview + 轮询：PreviewPlayer + 进度
	4.	Advanced Accordion：Resolution / Duration / Seed
	5.	Credits & Balance：扣费逻辑 + 红色不足提示
	6.	移动端适配：Drawer 抽屉 + Video 全屏
	7.	多模式复用：抽象字段映射，快速产出 Img2Vid 等

⸻

一句话

先用可组合的「壳 + 表单 + 预览」三个核心板块跑通 Txt2Vid， 色系沿用你的 pollo-dark 变量；表单字段、SideNav 以及 API hook 全部数据驱动。这样样式上 90 % 还原，代码上只写一次，后续任何新生成模式都是 “加一份字段映射” 的成本。祝你复刻愉快！🚀