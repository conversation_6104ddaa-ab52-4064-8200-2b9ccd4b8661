-- 视频生成任务主表
CREATE TABLE video_generation_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_uuid UUID NOT NULL,
  
  -- Kling API相关
  kling_task_id VARCHAR(255), -- Kling返回的任务ID
  kling_status VARCHAR(50),   -- Kling API返回的原始状态
  
  -- 任务信息
  mode VARCHAR(20) NOT NULL CHECK (mode IN ('txt2vid', 'img2vid', 'avatar')),
  model VARCHAR(50) NOT NULL, -- kling-v1, kling-v1-6
  prompt TEXT NOT NULL,
  parameters JSONB NOT NULL,  -- 存储所有生成参数
  
  -- 任务状态
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  
  -- 结果
  result_video_url TEXT,      -- 生成的视频URL
  result_thumbnail_url TEXT,  -- 缩略图URL
  result_duration INTEGER,    -- 视频时长(秒)
  result_file_size BIGINT,    -- 文件大小(字节)
  
  -- 错误信息
  error_message TEXT,
  error_code VARCHAR(50),
  
  -- 业务信息
  credits_used INTEGER NOT NULL DEFAULT 0,
  credits_refunded INTEGER DEFAULT 0, -- 失败时退还的积分
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  kling_created_at TIMESTAMP,  -- Kling任务创建时间
  completed_at TIMESTAMP,      -- 任务完成时间
  
  -- 索引
  INDEX idx_user_uuid (user_uuid),
  INDEX idx_status (status),
  INDEX idx_kling_task_id (kling_task_id),
  INDEX idx_created_at (created_at),
  INDEX idx_status_updated (status, updated_at) -- 用于后台任务查询
);

-- 任务状态变更日志表 (可选，用于调试和监控)
CREATE TABLE video_task_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  task_id UUID NOT NULL,
  
  -- 状态变更
  old_status VARCHAR(20),
  new_status VARCHAR(20),
  kling_status VARCHAR(50),
  
  -- API响应
  kling_response JSONB,       -- Kling API完整响应
  api_call_duration INTEGER,  -- API调用耗时(毫秒)
  
  -- 错误信息
  error_message TEXT,
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT NOW(),
  
  -- 索引
  INDEX idx_task_id (task_id),
  INDEX idx_created_at (created_at),
  
  -- 外键
  FOREIGN KEY (task_id) REFERENCES video_generation_tasks(id) ON DELETE CASCADE
);

-- 后台任务状态表 (用于管理轮询任务)
CREATE TABLE background_job_status (
  id INTEGER PRIMARY KEY AUTO_INCREMENT,
  job_name VARCHAR(100) NOT NULL UNIQUE, -- 'video_task_poller'
  last_run_at TIMESTAMP,
  next_run_at TIMESTAMP,
  is_running BOOLEAN DEFAULT FALSE,
  run_count INTEGER DEFAULT 0,
  error_count INTEGER DEFAULT 0,
  last_error TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 插入后台任务记录
INSERT INTO background_job_status (job_name, next_run_at) 
VALUES ('video_task_poller', NOW());

-- 触发器：自动更新 updated_at
CREATE TRIGGER update_video_tasks_updated_at
  BEFORE UPDATE ON video_generation_tasks
  FOR EACH ROW
  SET NEW.updated_at = NOW();

CREATE TRIGGER update_job_status_updated_at
  BEFORE UPDATE ON background_job_status
  FOR EACH ROW
  SET NEW.updated_at = NOW();
