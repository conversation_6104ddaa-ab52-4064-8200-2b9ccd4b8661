# 前端接入后端视频生成API集成方案

## 📋 项目概述

基于现有的前端UI组件和后端Kling视频生成封装，实现完整的视频生成工作流。

## 🏗️ 架构设计

```
前端UI层 → 状态管理层 → API调用层 → 后端处理层 → AI提供商
    ↓           ↓           ↓           ↓           ↓
ParamPanel → useGenerate → /api/video → aisdk/kling → Kling API
PreviewPane → Zustand    → fetch      → VideoModel  → 视频结果
```

## 🔧 实施步骤

### 阶段一：后端API路由实现

#### 1.1 创建视频生成API
```typescript
// src/app/api/video/generate/route.ts
import { generateVideo } from "@/aisdk/generate-video";
import { kling } from "@/aisdk/kling";
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { decreaseCredits, CreditsTransType } from "@/services/credit";

export async function POST(req: Request) {
  try {
    const {
      mode,
      model,
      prompt,
      parameters
    } = await req.json();

    // 用户认证
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("用户未认证");
    }

    // Credits计算和检查
    const creditsRequired = calculateCredits(model, parameters);
    
    // 扣除Credits
    await decreaseCredits({
      user_uuid,
      trans_type: CreditsTransType.VideoGeneration,
      credits: creditsRequired,
    });

    // 创建视频生成任务
    const videoModel = kling.video(model);
    const result = await generateVideo({
      model: videoModel,
      prompt,
      providerOptions: {
        kling: {
          duration: parameters.duration,
          aspect_ratio: parameters.resolution === "720p" ? "16:9" : "16:9",
          mode: "std"
        }
      }
    });

    return respData({
      taskId: generateTaskId(),
      videos: result.videos,
      creditsUsed: creditsRequired
    });

  } catch (error) {
    console.error("视频生成失败:", error);
    return respErr("视频生成失败");
  }
}
```

#### 1.2 创建状态查询API
```typescript
// src/app/api/video/status/route.ts
export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const taskIds = searchParams.get('taskIds')?.split(',') || [];
  
  // 查询任务状态逻辑
  const tasks = await Promise.all(
    taskIds.map(async (taskId) => {
      const task = await getTaskStatus(taskId);
      return {
        id: taskId,
        status: task.status,
        progress: task.progress,
        result: task.result,
        error: task.error
      };
    })
  );

  return respData({ tasks });
}
```

### 阶段二：前端状态管理

#### 2.1 创建生成Hook
```typescript
// src/hooks/useGenerate.ts
import { useState, useCallback } from 'react';
import useSWR from 'swr';

interface GenerationTask {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  result?: {
    videoUrl: string;
    thumbnailUrl?: string;
  };
  error?: string;
  parameters: GenerationParameters;
}

export function useGenerate() {
  const [tasks, setTasks] = useState<Map<string, GenerationTask>>(new Map());
  const [isGenerating, setIsGenerating] = useState(false);

  const generate = useCallback(async (params: GenerationParameters) => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/video/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const { data } = await response.json();
      
      // 添加任务到状态
      const newTask: GenerationTask = {
        id: data.taskId,
        status: 'pending',
        parameters: params,
      };
      
      setTasks(prev => new Map(prev.set(data.taskId, newTask)));
      
      return data.taskId;
    } catch (error) {
      console.error('生成失败:', error);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // 轮询状态
  const activeTasks = Array.from(tasks.values())
    .filter(task => task.status === 'pending' || task.status === 'processing')
    .map(task => task.id);

  const { data: statusData } = useSWR(
    activeTasks.length > 0 ? `/api/video/status?taskIds=${activeTasks.join(',')}` : null,
    (url) => fetch(url).then(res => res.json()),
    {
      refreshInterval: 3000,
      revalidateOnFocus: false
    }
  );

  // 更新任务状态
  useEffect(() => {
    if (statusData?.data?.tasks) {
      setTasks(prev => {
        const newTasks = new Map(prev);
        statusData.data.tasks.forEach((task: any) => {
          if (newTasks.has(task.id)) {
            newTasks.set(task.id, {
              ...newTasks.get(task.id)!,
              status: task.status,
              progress: task.progress,
              result: task.result,
              error: task.error
            });
          }
        });
        return newTasks;
      });
    }
  }, [statusData]);

  return {
    tasks: Array.from(tasks.values()),
    generate,
    isGenerating
  };
}
```

#### 2.2 创建Zustand Store
```typescript
// src/stores/generator.ts
import { create } from 'zustand';

interface GeneratorStore {
  currentMode: 'txt2vid' | 'img2vid' | 'avatar';
  tasks: Map<string, GenerationTask>;
  
  setMode: (mode: string) => void;
  addTask: (task: GenerationTask) => void;
  updateTask: (id: string, updates: Partial<GenerationTask>) => void;
  removeTask: (id: string) => void;
  
  // 选择器
  getTasksByStatus: (status: string) => GenerationTask[];
  getActiveTask: () => GenerationTask | null;
}

export const useGeneratorStore = create<GeneratorStore>((set, get) => ({
  currentMode: 'txt2vid',
  tasks: new Map(),
  
  setMode: (mode) => set({ currentMode: mode }),
  
  addTask: (task) => set((state) => ({
    tasks: new Map(state.tasks.set(task.id, task))
  })),
  
  updateTask: (id, updates) => set((state) => {
    const newTasks = new Map(state.tasks);
    const existingTask = newTasks.get(id);
    if (existingTask) {
      newTasks.set(id, { ...existingTask, ...updates });
    }
    return { tasks: newTasks };
  }),
  
  removeTask: (id) => set((state) => {
    const newTasks = new Map(state.tasks);
    newTasks.delete(id);
    return { tasks: newTasks };
  }),
  
  getTasksByStatus: (status) => {
    const tasks = get().tasks;
    return Array.from(tasks.values()).filter(task => task.status === status);
  },
  
  getActiveTask: () => {
    const tasks = get().tasks;
    return Array.from(tasks.values())
      .find(task => task.status === 'processing') || null;
  }
}));
```

### 阶段三：前端组件集成

#### 3.1 更新ParamPanel组件
```typescript
// src/components/workspace/param-panel.tsx
import { useGenerate } from "@/hooks/useGenerate";
import { useGeneratorStore } from "@/stores/generator";

export default function ParamPanel({ mode, title }: ParamPanelProps) {
  const { generate, isGenerating } = useGenerate();
  const { addTask } = useGeneratorStore();
  
  // 现有状态...
  
  const handleGenerate = async () => {
    try {
      const taskId = await generate({
        mode,
        model: selectedModel,
        prompt,
        parameters: {
          resolution,
          duration,
          seed,
          generateAudio,
          negativePrompt,
          outputCount,
          publicVisibility,
          copyProtection
        }
      });
      
      // 显示成功提示
      toast.success('视频生成任务已创建');
      
    } catch (error) {
      console.error('生成失败:', error);
      toast.error('生成失败，请重试');
    }
  };

  return (
    // 现有JSX...
    <GenerateButton
      onClick={handleGenerate}
      loading={isGenerating}
      disabled={!prompt.trim() || isGenerating}
      credits={userCredits}
      creditsRequired={calculateCredits(selectedModel, { duration, resolution })}
    />
  );
}
```

#### 3.2 更新PreviewPane组件
```typescript
// src/components/workspace/preview-pane.tsx
import { useGeneratorStore } from "@/stores/generator";

export default function PreviewPane() {
  const { tasks, getActiveTask } = useGeneratorStore();
  const activeTask = getActiveTask();
  const completedTasks = tasks.filter(task => task.status === 'completed');

  return (
    <Card className="flex-1 flex flex-col">
      <CardHeader>
        <h3 className="text-lg font-semibold">预览</h3>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col">
        {activeTask ? (
          <ProgressOverlay 
            progress={activeTask.progress || 0}
            status={activeTask.status}
          />
        ) : completedTasks.length > 0 ? (
          <VideoGrid videos={completedTasks} />
        ) : (
          <EmptyState />
        )}
      </CardContent>
    </Card>
  );
}
```

## 🔄 数据流程

1. **用户输入** → ParamPanel收集参数
2. **点击生成** → 调用useGenerate.generate()
3. **API调用** → POST /api/video/generate
4. **后端处理** → aisdk/kling生成视频
5. **状态轮询** → GET /api/video/status
6. **UI更新** → PreviewPane显示结果

## 📊 Credits计算

```typescript
const calculateCredits = (model: string, params: any) => {
  const baseCredits = {
    'kling-v1': 100,
    'kling-v1-6': 150
  };
  
  const durationMultiplier = params.duration / 5;
  const resolutionMultiplier = params.resolution === '1080p' ? 1.5 : 1;
  
  return Math.ceil(baseCredits[model] * durationMultiplier * resolutionMultiplier);
};
```

## 🚀 部署检查清单

- [ ] 环境变量配置 (KLING_ACCESS_KEY, KLING_SECRET_KEY)
- [ ] 数据库表创建 (video_generation_tasks)
- [ ] Credits系统集成
- [ ] 错误处理和用户反馈
- [ ] 文件存储配置 (R2/S3)
- [ ] API限流和安全检查

## 🔧 开发优先级

1. **Phase 1**: 基础API路由 + 简单前端调用
2. **Phase 2**: 状态管理 + 轮询机制
3. **Phase 3**: UI优化 + 错误处理
4. **Phase 4**: 高级功能 (批量生成、历史记录)

## 📝 详细实现步骤

### Step 1: 创建数据库模型

```sql
-- src/models/video-generation.sql
CREATE TABLE video_generation_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_uuid UUID NOT NULL REFERENCES users(uuid),
  mode VARCHAR(20) NOT NULL CHECK (mode IN ('txt2vid', 'img2vid', 'avatar')),
  model VARCHAR(50) NOT NULL,
  prompt TEXT NOT NULL,
  parameters JSONB NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  result_video_url TEXT,
  result_thumbnail_url TEXT,
  error_message TEXT,
  credits_used INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP,

  INDEX idx_user_uuid (user_uuid),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);
```

### Step 2: 数据库操作函数

```typescript
// src/models/video-generation.ts
import { db } from "@/lib/db";

export interface VideoGenerationTask {
  id: string;
  user_uuid: string;
  mode: string;
  model: string;
  prompt: string;
  parameters: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  result_video_url?: string;
  result_thumbnail_url?: string;
  error_message?: string;
  credits_used: number;
  created_at: Date;
  updated_at: Date;
  completed_at?: Date;
}

export async function createVideoTask(task: Omit<VideoGenerationTask, 'id' | 'created_at' | 'updated_at'>) {
  const result = await db.query(`
    INSERT INTO video_generation_tasks
    (user_uuid, mode, model, prompt, parameters, status, credits_used)
    VALUES ($1, $2, $3, $4, $5, $6, $7)
    RETURNING *
  `, [
    task.user_uuid,
    task.mode,
    task.model,
    task.prompt,
    JSON.stringify(task.parameters),
    task.status,
    task.credits_used
  ]);

  return result.rows[0];
}

export async function updateVideoTask(id: string, updates: Partial<VideoGenerationTask>) {
  const setClause = Object.keys(updates)
    .map((key, index) => `${key} = $${index + 2}`)
    .join(', ');

  const values = [id, ...Object.values(updates)];

  const result = await db.query(`
    UPDATE video_generation_tasks
    SET ${setClause}, updated_at = NOW()
    WHERE id = $1
    RETURNING *
  `, values);

  return result.rows[0];
}

export async function getVideoTasksByIds(ids: string[]) {
  const result = await db.query(`
    SELECT * FROM video_generation_tasks
    WHERE id = ANY($1)
    ORDER BY created_at DESC
  `, [ids]);

  return result.rows;
}

export async function getUserVideoTasks(user_uuid: string, limit = 20) {
  const result = await db.query(`
    SELECT * FROM video_generation_tasks
    WHERE user_uuid = $1
    ORDER BY created_at DESC
    LIMIT $2
  `, [user_uuid, limit]);

  return result.rows;
}
```

### Step 3: Credits计算服务

```typescript
// src/services/video-credits.ts
export const VIDEO_MODEL_CREDITS = {
  'kling-v1': 100,
  'kling-v1-6': 150,
} as const;

export const DURATION_MULTIPLIERS = {
  5: 1.0,
  10: 2.0,
} as const;

export const RESOLUTION_MULTIPLIERS = {
  '720p': 1.0,
  '1080p': 1.5,
} as const;

export function calculateVideoCredits(
  model: string,
  duration: number,
  resolution: string
): number {
  const baseCredits = VIDEO_MODEL_CREDITS[model as keyof typeof VIDEO_MODEL_CREDITS] || 100;
  const durationMult = DURATION_MULTIPLIERS[duration as keyof typeof DURATION_MULTIPLIERS] || 1.0;
  const resolutionMult = RESOLUTION_MULTIPLIERS[resolution as keyof typeof RESOLUTION_MULTIPLIERS] || 1.0;

  return Math.ceil(baseCredits * durationMult * resolutionMult);
}

export async function checkUserCredits(user_uuid: string, required: number): Promise<boolean> {
  const userCredits = await getUserCredits(user_uuid);
  return userCredits >= required;
}
```

### Step 4: 文件存储集成

```typescript
// src/services/video-storage.ts
import { newStorage } from "@/lib/storage";
import { getUuid } from "@/lib/hash";

export async function uploadVideoResult(
  videoBuffer: Buffer,
  taskId: string,
  format: string = 'mp4'
): Promise<{ videoUrl: string; thumbnailUrl?: string }> {
  const storage = newStorage();
  const filename = `video_${taskId}_${getUuid()}.${format}`;
  const key = `videos/${filename}`;

  try {
    const uploadResult = await storage.uploadFile({
      body: videoBuffer,
      key,
      contentType: `video/${format}`,
      disposition: "inline",
    });

    // TODO: 生成缩略图
    // const thumbnailUrl = await generateThumbnail(uploadResult.url);

    return {
      videoUrl: uploadResult.url,
      // thumbnailUrl
    };
  } catch (error) {
    console.error('视频上传失败:', error);
    throw new Error('视频上传失败');
  }
}
```

### Step 5: 错误处理和重试机制

```typescript
// src/lib/video-retry.ts
export class VideoGenerationError extends Error {
  constructor(
    message: string,
    public code: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'VideoGenerationError';
  }
}

export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      if (i === maxRetries) break;

      // 检查是否可重试
      if (error instanceof VideoGenerationError && !error.retryable) {
        break;
      }

      // 指数退避
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }

  throw lastError!;
}
```

## 🧪 测试策略

### 单元测试
```typescript
// __tests__/video-generation.test.ts
import { calculateVideoCredits } from '@/services/video-credits';

describe('Video Credits Calculation', () => {
  test('should calculate basic credits correctly', () => {
    expect(calculateVideoCredits('kling-v1', 5, '720p')).toBe(100);
    expect(calculateVideoCredits('kling-v1-6', 10, '1080p')).toBe(450);
  });
});
```

### 集成测试
```typescript
// __tests__/api/video-generate.test.ts
import { POST } from '@/app/api/video/generate/route';

describe('/api/video/generate', () => {
  test('should create video generation task', async () => {
    const request = new Request('http://localhost/api/video/generate', {
      method: 'POST',
      body: JSON.stringify({
        mode: 'txt2vid',
        model: 'kling-v1',
        prompt: 'A cat playing',
        parameters: {
          duration: 5,
          resolution: '720p'
        }
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(data.code).toBe(0);
    expect(data.data.taskId).toBeDefined();
  });
});
```

## 🔍 监控和日志

```typescript
// src/lib/video-monitoring.ts
export function logVideoGeneration(
  taskId: string,
  event: string,
  metadata?: any
) {
  console.log(`[VIDEO_GEN] ${taskId} - ${event}`, metadata);

  // 发送到监控服务
  // analytics.track('video_generation', {
  //   taskId,
  //   event,
  //   ...metadata
  // });
}
```

## 📱 移动端适配

```typescript
// src/components/workspace/mobile-video-player.tsx
export function MobileVideoPlayer({ videoUrl }: { videoUrl: string }) {
  return (
    <div className="relative w-full aspect-video">
      <video
        src={videoUrl}
        controls
        className="w-full h-full object-cover rounded-lg"
        playsInline
        preload="metadata"
      />
    </div>
  );
}
```
