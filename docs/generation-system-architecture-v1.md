# 🏗️ 重新设计方案

## 🔍 当前设计的问题

### 1. **硬编码提供商**
- `kling_task_id` 字段写死了 Kling
- 后续增加 Runway、Pika、Luma 等模型时需要修改数据库结构

### 2. **单一任务类型**
- 只考虑了视频生成
- 图片生成、音频生成、img2video 等需要重新设计

### 3. **紧耦合的服务层**
- 视频生成服务直接调用 Kling API
- 难以扩展到其他提供商

## 🏗️ 重新设计方案

### 方案一：通用生成任务架构（推荐）

#### 1. **数据库重构**
```sql
-- 通用生成任务表
CREATE TABLE cp_generation_jobs (
  id SERIAL PRIMARY KEY,
  uuid VARCHAR(255) UNIQUE NOT NULL,
  user_uuid VARCHAR(255) NOT NULL,
  
  -- 任务类型和模式
  task_type VARCHAR(50) NOT NULL, -- video, image, audio, etc.
  generation_mode VARCHAR(50) NOT NULL, -- txt2vid, img2vid, txt2img, etc.
  
  -- 提供商和模型信息
  provider VARCHAR(100) NOT NULL, -- kling, runway, pika, openai, etc.
  model VARCHAR(100) NOT NULL,
  
  -- 输入参数
  prompt TEXT,
  negative_prompt TEXT,
  input_files JSONB, -- 存储输入文件URL（img2vid等需要）
  parameters JSONB, -- 所有生成参数
  
  -- 提供商特定信息
  provider_task_id VARCHAR(255), -- 通用的外部任务ID
  provider_metadata JSONB, -- 提供商特定的元数据
  
  -- 任务状态
  status VARCHAR(50) NOT NULL,
  progress INTEGER DEFAULT 0, -- 0-100 进度百分比
  
  -- 结果信息
  output_files JSONB, -- 存储生成结果的文件信息
  result_metadata JSONB, -- 结果的元数据（尺寸、时长等）
  
  -- 成本和时间
  credits_cost INTEGER NOT NULL,
  estimated_duration INTEGER, -- 预估完成时间（秒）
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- 错误信息
  error_message TEXT,
  retry_count INTEGER DEFAULT 0
);
```

#### 2. **服务层架构**
```
GenerationService (统一入口)
├── VideoGenerationService
│   ├── KlingVideoProvider
│   ├── RunwayVideoProvider
│   └── PikaVideoProvider
├── ImageGenerationService
│   ├── KlingImageProvider
│   ├── OpenAIImageProvider
│   └── MidjourneyProvider
└── AudioGenerationService
    ├── ElevenLabsProvider
    └── OpenAIAudioProvider
```

#### 3. **Provider 接口标准化**
```typescript
interface GenerationProvider {
  readonly name: string;
  readonly supportedTaskTypes: TaskType[];
  readonly supportedModes: GenerationMode[];
  
  createTask(params: GenerationParams): Promise<ProviderTask>;
  queryTask(taskId: string): Promise<TaskStatus>;
  cancelTask(taskId: string): Promise<void>;
  
  // 可选的进度回调
  onProgress?(callback: (progress: number) => void): void;
}
```

#### 4. **前端组件重构**
```
GeneratorWorkspace (通用)
├── TaskTypeSelector (video/image/audio)
├── ModeSelector (txt2vid/img2vid/txt2img)
├── ProviderSelector (kling/runway/openai)
├── ParameterPanel (动态根据provider和mode)
└── ResultsPanel (通用结果展示)
```

### 方案二：微服务架构

#### 1. **按任务类型拆分服务**
- `video-generation-service`
- `image-generation-service`
- `audio-generation-service`

#### 2. **统一的任务调度器**
- `task-scheduler-service`
- 负责任务分发和状态管理

#### 3. **Provider 插件系统**
- 每个 Provider 作为独立的插件
- 支持热插拔和动态加载

## 🎯 推荐的迭代步骤

### 第一阶段：重构现有视频生成
1. **数据库迁移**：将现有表结构改为通用结构
2. **Provider 抽象**：创建 Provider 接口和 Kling 实现
3. **服务层重构**：将硬编码的 Kling 调用改为 Provider 模式

### 第二阶段：扩展任务类型
1. **图片生成支持**：添加 txt2img、img2img 模式
2. **多 Provider 支持**：添加 OpenAI、Runway 等
3. **前端组件通用化**：支持动态切换任务类型和 Provider

### 第三阶段：高级功能
1. **批量任务**：支持一次生成多个结果
2. **任务队列**：优化资源使用和用户体验
3. **结果管理**：版本控制、收藏、分享等

## 🔧 技术考虑

### 1. **配置驱动**
```typescript
// 配置文件定义支持的组合
const GENERATION_CONFIG = {
  video: {
    providers: ['kling', 'runway', 'pika'],
    modes: ['txt2vid', 'img2vid'],
    defaultProvider: 'kling'
  },
  image: {
    providers: ['kling', 'openai', 'midjourney'],
    modes: ['txt2img', 'img2img'],
    defaultProvider: 'openai'
  }
};
```

### 2. **动态 UI 生成**
- 根据选择的 Provider 和模式动态生成参数表单
- 统一的参数验证和转换逻辑

### 3. **成本计算抽象**
```typescript
interface CostCalculator {
  calculateCost(params: GenerationParams): number;
}
```

### 4. **结果标准化**
```typescript
interface GenerationResult {
  files: GeneratedFile[];
  metadata: ResultMetadata;
  provider: string;
  model: string;
}
```

## 🚀 优势

1. **高扩展性**：新增 Provider 只需实现接口
2. **类型安全**：完整的 TypeScript 类型定义
3. **配置驱动**：通过配置文件控制功能开关
4. **用户体验**：统一的界面，一致的操作流程
5. **维护性**：清晰的架构分层，易于测试和调试
