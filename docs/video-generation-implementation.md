# 视频生成系统实现指南

## 📋 实现概览

本系统基于 Kling AI 实现了完整的视频生成流程，使用项目已封装的 AI SDK：
- 前端视频生成界面
- 后端 API 服务（使用 `generateVideo` 和 `kling.video()` 方法）
- 数据库任务管理
- R2 存储集成
- 积分系统集成

## 🔧 技术特点

- **使用现有 AI SDK**：充分利用项目中已封装的 `generateVideo` 方法
- **统一的 Provider 模式**：使用 `kling.video()` 创建视频模型
- **类型安全**：完整的 TypeScript 类型支持
- **异步处理**：视频生成在后台异步处理，前端实时轮询状态

## 🏗️ 系统架构

```
前端 (React/Next.js)
├── GeneratorWorkspace (工作区布局)
├── ParamPanel (参数面板)
├── PreviewPane (预览面板)
└── useVideoGeneration (状态管理)

后端 API
├── /api/video/generate (创建生成任务)
├── /api/video/status (查询任务状态)
└── /api/video/test (系统测试)

数据层
├── generation_jobs (任务表)
├── Kling SDK (视频生成)
├── R2 Storage (视频存储)
└── Credits System (积分管理)
```

## 🚀 部署步骤

### 1. 数据库迁移

执行以下 SQL 创建必要的表：

```sql
CREATE TABLE cp_generation_jobs (
  id SERIAL PRIMARY KEY,
  uuid VARCHAR(255) UNIQUE NOT NULL,
  user_uuid VARCHAR(255) NOT NULL,
  mode VARCHAR(50) NOT NULL, -- txt2vid, img2vid, avatar
  model VARCHAR(100) NOT NULL,
  prompt TEXT,
  negative_prompt TEXT,
  parameters JSONB,
  kling_task_id VARCHAR(255),
  status VARCHAR(50) NOT NULL, -- pending, processing, completed, failed
  result_url VARCHAR(500),
  credits_cost INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT
);

-- 创建索引
CREATE INDEX idx_generation_jobs_user_uuid ON cp_generation_jobs(user_uuid);
CREATE INDEX idx_generation_jobs_status ON cp_generation_jobs(status);
CREATE INDEX idx_generation_jobs_created_at ON cp_generation_jobs(created_at);
```

### 2. 环境变量配置

确保以下环境变量已配置：

```env
# Kling AI API
KLING_ACCESS_KEY=your_kling_access_key
KLING_SECRET_KEY=your_kling_secret_key

# R2 Storage
STORAGE_ENDPOINT=your_r2_endpoint
STORAGE_ACCESS_KEY=your_r2_access_key
STORAGE_SECRET_KEY=your_r2_secret_key
STORAGE_BUCKET=your_r2_bucket
STORAGE_DOMAIN=your_r2_domain

# Database
DATABASE_URL=your_database_url
```

### 3. 依赖安装

所有必要的依赖已在 package.json 中，运行：

```bash
pnpm install
```

## 💻 核心代码示例

### 使用 AI SDK 生成视频

```typescript
import { generateVideo } from "@/aisdk";
import { kling } from "@/aisdk/kling";

// 创建视频模型
const videoModel = kling.video("kling-v1");

// 生成视频
const result = await generateVideo({
  model: videoModel,
  prompt: "A beautiful sunset over the ocean",
  providerOptions: {
    kling: {
      negative_prompt: "blurry, low quality",
      duration: 5,
      aspect_ratio: "16:9",
      cfg_scale: 0.5,
      mode: "std",
    },
  },
});

// 获取生成的视频数据
const video = result.videos[0];
const videoData = video.uint8Array; // 可直接上传到 R2
```

### 前端使用 Hook

```typescript
import { useVideoGeneration, useVideoStatus } from "@/hooks/use-video-generation";

function VideoGenerator() {
  const { generateVideo, isGenerating } = useVideoGeneration();
  const { job, isCompleted } = useVideoStatus(jobUuid);

  const handleGenerate = async () => {
    const jobUuid = await generateVideo({
      mode: "txt2vid",
      model: "kling-v1",
      prompt: "Your prompt here",
      parameters: { duration: 5, resolution: "720p" }
    });
  };

  return (
    <div>
      <button onClick={handleGenerate} disabled={isGenerating}>
        {isGenerating ? "Generating..." : "Generate Video"}
      </button>
      {isCompleted && <video src={job?.result_url} controls />}
    </div>
  );
}
```

## 🧪 测试流程

### 1. 系统测试

```bash
# 启动开发服务器
pnpm dev

# 测试系统状态
curl -X GET "http://localhost:3000/api/video/test" \
  -H "Authorization: Bearer your_api_key"
```

### 2. 视频生成测试

使用 `debug/video-generation-test.http` 文件进行完整测试：

1. 测试系统状态
2. 创建视频生成任务
3. 查询任务状态
4. 验证视频存储

### 3. 前端测试

1. 访问 `/txt2vid` 页面
2. 填写生成参数
3. 点击生成按钮
4. 观察状态更新和结果显示

## 📊 监控和调试

### 日志监控

关键日志点：
- 任务创建：`Video generation job created`
- Kling API 调用：`request text2video`
- 任务状态更新：`Polling attempt`
- 视频上传：`upload file`

### 常见问题

1. **积分不足**
   - 检查用户积分余额
   - 验证积分计算逻辑

2. **Kling API 错误**
   - 检查 API 密钥配置
   - 验证请求参数格式

3. **存储上传失败**
   - 检查 R2 配置
   - 验证网络连接

4. **任务轮询超时**
   - 调整轮询间隔和最大尝试次数
   - 检查 Kling 服务状态

## 🔧 配置优化

### 性能优化

1. **数据库优化**
   - 添加适当索引
   - 定期清理过期任务

2. **前端优化**
   - 调整轮询频率
   - 实现任务缓存

3. **存储优化**
   - 配置 CDN 加速
   - 实现视频压缩

### 扩展功能

1. **批量生成**
   - 支持多个视频同时生成
   - 队列管理

2. **历史记录**
   - 用户生成历史
   - 收藏和分享功能

3. **高级参数**
   - 更多模型选项
   - 自定义参数配置

## 📈 使用统计

系统提供以下统计信息：
- 生成任务数量
- 成功率统计
- 积分消耗统计
- 用户活跃度

## 🛡️ 安全考虑

1. **API 安全**
   - 用户身份验证
   - 请求频率限制

2. **数据安全**
   - 敏感信息加密
   - 访问权限控制

3. **存储安全**
   - 文件访问控制
   - 定期备份

## 📞 技术支持

如遇问题，请检查：
1. 环境变量配置
2. 数据库连接
3. API 密钥有效性
4. 网络连接状态

系统已完全集成现有的用户系统、积分系统和存储系统，确保与项目整体架构的一致性。
